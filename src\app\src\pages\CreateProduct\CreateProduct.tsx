import React, { useState, useRef, useEffect } from 'react';

import { t } from 'i18next';
import { routes } from 'pages/routes';
import { PhlexButton, PhlexFormSkeleton, PhlexLayout, PhlexLeftStep } from 'phlex-core-ui';
import { useNavigate } from 'react-router-dom';
import { BasePathType } from 'types/path';

import { Breadcrumbs } from './breadcrumbs';
import StyledComponents from './CreateProduct.styles';
import { Infrastructure, InfrastructureProps } from './Infrastructure';
import { Management, ManagementProps } from './Management';
import { Overview, OverviewProps } from './Overview';
import data from './productSteps.json';
import { Services, ServicesProps } from './Services';
import { Summary, SummaryProps } from './Summary';

export type DataType = {
  stepNumber: number;
  stepId: string;
  name: string;
  title: string;
  description: string;
};

export const CreateProduct = ({ basepath }: BasePathType): JSX.Element => {
  type FormRefType = {
    trigger: (name?: string) => Promise<boolean>;
    getValues: () => Record<string, unknown>;
    getFieldState?: (name: string) => { invalid: boolean };
  };

  const navigate = useNavigate();
  const overviewRef = useRef<FormRefType | null>(null);
  const managementRef = useRef<FormRefType | null>(null);
  const infrastructureRef = useRef<FormRefType | null>(null);
  const servicesRef = useRef<FormRefType | null>(null);
  const summaryRef = useRef<FormRefType | null>(null);

  const { StyledLeftWrapper, StyledLeftPanel, ProductWrapper } = StyledComponents;

  const [overviewState, setOverviewState] = useState<OverviewProps['defaultValues']>({});
  const [managementState, setManagementState] = useState<ManagementProps['defaultValues']>({});
  const [infrastructureState, setInfrastructureState] = useState<InfrastructureProps['defaultValues']>({});
  const [servicesState, setServicesState] = useState<ServicesProps['defaultValues']>({});
  const [summaryState, setSummaryState] = useState<SummaryProps['defaultValues']>({});
  const [formStates, setFormStates] = useState<{ [step: number]: Record<string, unknown> }>({});
  const [currentStep, setCurrentStep] = useState(0);
  const [validSteps, setValidSteps] = useState<boolean[]>(Array(data.totalSteps).fill(false));
  const [totalSteps, setTotalSteps] = useState(data.totalSteps);

  const { PageTopBar, PageActions } = PhlexLayout;

  const validateAndNavigate = (direction: 'back' | 'next'): void => {
    if (direction === 'back') {
      navigateBack();
    }
    if (direction === 'next' && currentStep < totalSteps - 1) {
      navigateNext();
    }
  };

  const navigateBack = (): void => {
    if (currentStep === 0) {
      navigate(`${basepath}${routes.products}`);
    }
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const navigateNext = (): void => {
    // Validate current step before moving forward
    if (currentStep === 0 && overviewRef.current) {
      overviewRef.current.trigger().then((valid: boolean) => {
        updateStepValidity(0, valid);

        if (valid) {
          setCurrentStep(currentStep + 1);
          // TODO: save data
        }
      });
    } else if (currentStep === 1 && managementRef.current) {
      managementRef.current.trigger().then((valid: boolean) => {
        updateStepValidity(1, valid);

        if (valid) {
          setCurrentStep(currentStep + 1);
          // TODO: save data
        }
      });
    } else if (currentStep === 2 && infrastructureRef.current) {
      infrastructureRef.current.trigger().then((valid: boolean) => {
        updateStepValidity(2, valid);

        if (valid) {
          setCurrentStep(currentStep + 1);
          // TODO: save data
        }
      });
    } else if (currentStep === 3 && servicesRef.current) {
      servicesRef.current.trigger().then((valid: boolean) => {
        updateStepValidity(3, valid);

        if (valid) {
          setCurrentStep(currentStep + 1);
          // TODO: save data
        }
      });
    }
  };

  const updateStepValidity = (step: number, valid: boolean): void => {
    setValidSteps((prev) => {
      const updated = [...prev];
      updated[step] = valid;
      return updated;
    });
  };

  return (
    <>
      <PageTopBar>
        <Breadcrumbs basepath={basepath} />
        <PageActions>
          <PhlexButton
            label={currentStep === 0 ? t('Shared.Cancel') : t('Shared.Back')}
            color="secondary"
            onClick={() => validateAndNavigate('back')}
          />
          <PhlexButton label={t('Shared.Next')} onClick={() => validateAndNavigate('next')} disabled={currentStep === totalSteps - 1} />
        </PageActions>
      </PageTopBar>

      {!data?.steps || !Array.isArray(data.steps) ? (
        <PhlexFormSkeleton />
      ) : (
        <StyledLeftWrapper>
          <StyledLeftPanel>
            <>
              {data.steps.map((step, index) => {
                const isActive = currentStep === index;
                const isComplete = index < currentStep;
                const isEnabled = isActive || isComplete;

                return (
                  <PhlexLeftStep
                    key={`step-${step.stepNumber}`}
                    label={step.name}
                    step={`${index + 1}`}
                    icon={step.icon}
                    active={isActive}
                    disabled={!isEnabled}
                    complete={isComplete}
                    onClick={isEnabled ? () => setCurrentStep(index) : undefined}
                  />
                );
              })}
            </>
          </StyledLeftPanel>

          <ProductWrapper>
            {currentStep === 0 && (
              <Overview
                ref={overviewRef}
                defaultValues={overviewState}
                onFormChange={(values: Record<string, unknown>, fieldName?: string) => {
                  setOverviewState(values);

                  // Only validate on key press if the field is invalid
                  if (overviewRef.current && fieldName && overviewRef.current.getFieldState) {
                    const fieldState = overviewRef.current.getFieldState(fieldName);

                    if (fieldState?.invalid) {
                      overviewRef.current.trigger(fieldName).then((valid: boolean) => updateStepValidity(0, valid));
                    }
                  }
                }}
              />
            )}

            {currentStep === 1 && (
              <Management
                ref={managementRef}
                defaultValues={managementState}
                onFormChange={(values: Record<string, unknown>, fieldName?: string) => {
                  setManagementState(values);

                  // Only validate on key press if the field is invalid
                  if (managementRef.current && fieldName && managementRef.current.getFieldState) {
                    const fieldState = managementRef.current.getFieldState(fieldName);

                    if (fieldState?.invalid) {
                      managementRef.current.trigger(fieldName).then((valid: boolean) => updateStepValidity(0, valid));
                    }
                  }
                }}
              />
            )}

            {currentStep === 2 && (
              <Infrastructure
                ref={infrastructureRef}
                defaultValues={infrastructureState}
                onFormChange={(values: Record<string, unknown>, fieldName?: string) => {
                  setInfrastructureState(values);

                  // Only validate on key press if the field is invalid
                  if (infrastructureRef.current && fieldName && infrastructureRef.current.getFieldState) {
                    const fieldState = infrastructureRef.current.getFieldState(fieldName);

                    if (fieldState?.invalid) {
                      infrastructureRef.current.trigger(fieldName).then((valid: boolean) => updateStepValidity(0, valid));
                    }
                  }
                }}
              />
            )}

            {currentStep === 3 && (
              <Services
                ref={servicesRef}
                defaultValues={servicesState}
                onFormChange={(values: Record<string, unknown>, fieldName?: string) => {
                  setServicesState(values);

                  // Only validate on key press if the field is invalid
                  if (servicesRef.current && fieldName && servicesRef.current.getFieldState) {
                    const fieldState = servicesRef.current.getFieldState(fieldName);

                    if (fieldState?.invalid) {
                      servicesRef.current.trigger(fieldName).then((valid: boolean) => updateStepValidity(0, valid));
                    }
                  }
                }}
              />
            )}

            {currentStep === 4 && (
              <Summary
                ref={summaryRef}
                defaultValues={summaryState}
                onFormChange={(values: Record<string, unknown>, fieldName?: string) => {
                  setSummaryState(values);

                  // Only validate on key press if the field is invalid
                  if (summaryRef.current && fieldName && summaryRef.current.getFieldState) {
                    const fieldState = summaryRef.current.getFieldState(fieldName);

                    if (fieldState?.invalid) {
                      summaryRef.current.trigger(fieldName).then((valid: boolean) => updateStepValidity(0, valid));
                    }
                  }
                }}
              />
            )}
          </ProductWrapper>
        </StyledLeftWrapper>
      )}
    </>
  );
};
