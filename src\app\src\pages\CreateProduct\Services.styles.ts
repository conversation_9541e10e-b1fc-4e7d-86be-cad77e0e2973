import styled from 'styled-components';

export default {
  ServiceWrapper: styled.div`
    background-color: ${({ theme }) => theme.colors.backgroundPri};
    border-radius: ${({ theme }) => theme.radius};
    box-shadow: ${({ theme }) => theme.shadow};
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-left: 1.25rem;
    margin-right: 1.25rem;
    margin-bottom: 1.25rem;
    max-width: 45rem;
    padding: 1.25rem;
  `,

  Container: styled.div``,

  ServiceHeader: styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  `,

  ServiceTitle: styled.h4`
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;

    &:hover {
      background-color: ${({ theme }) => theme.colors.backgroundPri};
    }
  `,

  ServiceTitleInput: styled.input`
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    border: 0.0625rem solid #d1d5db;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    background-color: ${({ theme }) => theme.colors.backgroundPri};
    outline: none;
    min-width: 12.5rem;
    margin-bottom: 1.25rem;
  `,

  AddServiceContainer: styled.div`
    background-color: ${({ theme }) => theme.colors.backgroundPri};
    border-radius: ${({ theme }) => theme.radius};
    box-shadow: ${({ theme }) => theme.shadow};
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-left: 1.25rem;
    margin-right: 1.25rem;
    margin-bottom: 1.25rem;
    max-width: 45rem;
    padding: 1.25rem;
  `,

  ActionButton: styled.div<{ $variant?: 'add' | 'remove' }>`
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: ${({ $variant, theme }) => ($variant === 'remove' ? theme.colors.red : theme.colors.blue)};

    .icon-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: ${({ $variant }) => ($variant === 'remove' ? '#fee2e2' : '#dbeafe')};
      border: 1px solid ${({ $variant }) => ($variant === 'remove' ? '#fecaca' : '#bfdbfe')};
    }
  `,
};
