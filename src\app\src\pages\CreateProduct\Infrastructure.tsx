import React, { forwardRef, useEffect, useImperativeHandle } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { PhlexDropdown, PhlexLayout, PhlexMultiSelect } from 'phlex-core-ui';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { useForm, FormProvider, Controller } from 'react-hook-form';

import StyledComponents from './CreateProduct.styles';
import { buildYupSchema } from './yupSchema';

export type InfrastructureProps = {
  defaultValues?: Record<string, unknown>;
  onFormChange?: (values: Record<string, unknown>) => void;
};

const Infrastructure = forwardRef<unknown, InfrastructureProps>(({ defaultValues, onFormChange }, ref) => {
  const { SectionHeading, SectionSubheading } = PhlexLayout;
  const { ProductForm, StyledSectionWrapper } = StyledComponents;

  const fields = [
    {
      name: 'azuredomain',
      displayName: t('CreateProduct.Infrastructure.AzureDomain'),
      inputType: 'DROPDOWN',
      options: ['Smartphlex Core', 'Shared Platform Apps'],
      validate: { required: true },
    },
    {
      name: 'azureresources',
      displayName: t('CreateProduct.ProductManagement.AzureResources.Title'),
      inputType: 'MULTISELECT',
      options: ['CosmosDB', 'MySQLdb', 'RedisCache', 'SearchService', 'ServiceBus', 'StorageAccount', 'PostgreSQL'],
    },
  ];

  const schema = React.useMemo(() => buildYupSchema(fields), [t]);

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues: defaultValues || { documentname: '', description: '' },
  });

  useEffect(() => {
    if (defaultValues) {
      methods.reset(defaultValues);
    }
  }, [defaultValues]);

  useImperativeHandle(ref, () => ({
    trigger: methods.trigger,
    getValues: methods.getValues,
  }));

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = (data: Record<string, unknown>): void => {
    console.log('Product Infrastructure form submitted:', data);
  };

  // Watch all form values and notify parent on change
  React.useEffect(() => {
    if (onFormChange) {
      const subscription = methods.watch((values: Record<string, unknown>) => {
        onFormChange(values);
      });
      return () => subscription.unsubscribe();
    }
  }, [methods, onFormChange]);

  return (
    <FormProvider {...methods}>
      <ProductForm onSubmit={handleSubmit(onSubmit)}>
        <StyledSectionWrapper>
          <SectionHeading>{t('CreateProduct.Infrastructure.SectionTitle')}</SectionHeading>
          <SectionSubheading>{t('CreateProduct.Infrastructure.SectionSubTitle')}</SectionSubheading>
        </StyledSectionWrapper>
        <Controller
          name="azuredomain"
          control={control}
          render={({ field }) => (
            <PhlexDropdown
              key={field.name}
              name={field.name}
              required
              label={t('CreateProduct.Infrastructure.AzureDomain')}
              data={fields[0].options}
              width="fullwidth"
              value={field.value}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
        <Controller
          name="azureresources"
          control={control}
          rules={{
            required: true,
          }}
          render={({ field }) => (
            <PhlexMultiSelect
              key={field.name}
              name={field.name}
              label={t('CreateProduct.Infrastructure.AzureResources.Title')}
              data={fields[1].options}
              width="fullwidth"
              value={field.value as IOption[]}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              fetchData={function (..._args: unknown[]): Promise<IOption[]> {
                return Promise.resolve(fields[1].options?.map((option) => ({ value: option, label: option })) as IOption[]);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      </ProductForm>
    </FormProvider>
  );
});

Infrastructure.displayName = 'Infrastructure';

export { Infrastructure };
