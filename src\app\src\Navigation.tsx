import React from 'react';

import { PhlexNavItem } from 'phlex-core-ui';
import { useTranslation } from 'react-i18next';
import { removeTrailingSlashFromPath } from 'shared/services/urlUtils';
import './multilingual/i18n';

interface NavigationProps {
  basepath: string;
}

const Navigation = ({ basepath }: NavigationProps): JSX.Element => {
  const { t } = useTranslation();
  const cleanBasePath = removeTrailingSlashFromPath(basepath);

  return <PhlexNavItem text={t('Nav.Templates')} to={`${cleanBasePath}/`} />;
};

export default Navigation;
