{"name": "ui", "version": "0.0.0", "private": true, "dependencies": {"@hookform/resolvers": "^5.1.1", "axios": "^1.8.2", "axon-core-ui-shared": "^1.1.146-dev", "i18next": "^25.3.1", "phlex-core-ui": "1.1.972", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.59.0", "react-i18next": "^15.6.0", "react-router-dom": "7.6.3", "react-toastify": "^11.0.5", "styled-components": "6.1.16", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.6", "@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-licensing": "^1.6.0", "@progress/kendo-react-animation": "^11.2.0", "@progress/kendo-react-buttons": "^11.2.0", "@progress/kendo-react-dropdowns": "^11.2.0", "@progress/kendo-react-inputs": "^11.2.0", "@progress/kendo-react-intl": "^11.2.0", "@progress/kendo-react-treeview": "^11.2.0", "@progress/kendo-theme-default": "^11.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/copy-webpack-plugin": "^10.1.3", "@types/enzyme": "3.10.19", "@types/fork-ts-checker-webpack-plugin": "^0.4.5", "@types/jest": "^30.0.0", "@types/node": "^24.0.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@types/react-toastify": "^4.1.0", "@types/styled-components": "^5.1.34", "@types/webpack": "^5.28.5", "@types/webpack-dev-server": "^4.7.2", "@types/yup": "^0.32.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "babel-jest": "^30.0.4", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "dotenv": "^17.0.1", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-testing-library": "^7.5.3", "eslint-webpack-plugin": "^5.0.2", "fork-ts-checker-webpack-plugin": "^9.1.0", "html-webpack-plugin": "^5.6.3", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "jest-junit": "^16.0.0", "jest-localstorage-mock": "^2.4.26", "prettier": "^3.6.2", "style-loader": "^4.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.8.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-shell-plugin-next": "^2.3.2"}, "jest-junit": {"outputDirectory": "/testResults", "outputName": "test-report.xml"}, "resolutions": {"strip-ansi": "6.0.1", "string-width": "4.2.3", "wrap-ansi": "7.0.0"}, "scripts": {"start": "bash ./config/generate-ssl.sh && yarn run webpack serve --config webpack.dev.config.ts", "build": "yarn run webpack --config webpack.prod.config.ts", "test": "jest --passWithNoTests --env=jsdom", "test:coverage": "jest --passWithNoTests --coverage --env=jsdom", "test:ci": "jest --ci --reporters=default --reporters=jest-junit --passWithNoTests --env=jsdom", "lint": "eslint", "lint-fix": "eslint --fix", "vs-code-launch": "yarn install && yarn start"}}