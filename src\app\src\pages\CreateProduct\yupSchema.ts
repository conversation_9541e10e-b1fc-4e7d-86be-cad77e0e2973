import { t } from 'i18next';
import * as yup from 'yup';

import { FieldType } from './FormFields';

const getTextValidator = (field: FieldType): yup.StringSchema<string | undefined, yup.AnyObject, undefined, ''> => {
  let validator = yup.string();
  if (field.validate?.required) validator = validator.required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  if (field.validate?.minLength)
    validator = validator.min(
      field.validate.minLength,
      t('DataScreen.Forms.InsertMinCharacters', { number: field.validate.minLength.toString() })
    );
  if (field.validate?.maxLength)
    validator = validator.max(
      field.validate.maxLength,
      t('DataScreen.Forms.InsertMaxCharacters', { number: field.validate.maxLength.toString() })
    );
  if (field.validate?.regEx) {
    validator = validator.matches(
      new RegExp(field.validate.regEx),
      field.validate?.regExMessage || t('DataScreen.Forms.ValidField', { field: field.displayName })
    );
  }
  return validator;
};

const getMultiTextValidator = (field: FieldType): yup.ArraySchema<unknown[] | null | undefined, yup.AnyObject, undefined, ''> => {
  if (field.validate?.required) {
    return yup
      .array()
      .defined()
      .min(1, t('DataScreen.Forms.RequiredField', { field: field.displayName }))
      .required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  } else {
    return yup.array().nullable();
  }
};

const getNumberValidator = (field: FieldType): yup.NumberSchema<number | undefined, yup.AnyObject, undefined, ''> => {
  let validator = yup.number();
  if (field.validate?.required) validator = validator.required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  if (field.validate?.minLength)
    validator = validator.min(
      field.validate.minLength,
      t('DataScreen.Forms.InsertMinCharacters', { number: field.validate.minLength.toString() })
    );
  if (field.validate?.maxLength)
    validator = validator.max(
      field.validate.maxLength,
      t('DataScreen.Forms.InsertMaxCharacters', { number: field.validate.maxLength.toString() })
    );
  return validator;
};

const getDropdownValidator = (field: FieldType): yup.MixedSchema => {
  let validator = yup.mixed();
  if (field.validate?.required) validator = validator.required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  return validator;
};

const getMultiSelectValidator = (field: FieldType): yup.ArraySchema<unknown[] | null | undefined, yup.AnyObject, undefined, ''> => {
  let validator = yup.array();
  if (field.validate?.required)
    validator = validator
      .min(1, t('DataScreen.Forms.RequiredField', { field: field.displayName }))
      .required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  return validator.nullable();
};

const getDatePickerValidator = (field: FieldType): yup.DateSchema<Date | undefined, yup.AnyObject, undefined, ''> => {
  let validator = yup.date();
  if (field.validate?.required) validator = validator.required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  return validator;
};

const getFileValidator = (field: FieldType): yup.MixedSchema => {
  let validator = yup.mixed();
  if (field.validate?.required) validator = validator.required(t('DataScreen.Forms.RequiredField', { field: field.displayName }));
  if (field.validate?.allowedExtensions) {
    const exts = field.validate.allowedExtensions.split(',').map((e) => e.trim().toLowerCase());
    validator = validator.test('fileType', 'Invalid file type', (value: unknown) => {
      if (!value || typeof value !== 'object' || !('name' in value) || typeof (value as { name?: unknown }).name !== 'string') return true;
      const file = value as { name: string };
      const ext = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
      return exts.includes(ext);
    });
  }
  return validator;
};

export function buildYupSchema(fields?: FieldType[]): yup.ObjectSchema<Record<string, unknown>> {
  const shape: Record<string, yup.AnySchema> = {};
  fields?.forEach((field) => {
    switch (field.inputType) {
      case 'TEXT':
        shape[field.name] = getTextValidator(field);
        break;
      case 'MULTITEXT':
        shape[field.name] = getMultiTextValidator(field);
        break;
      case 'NUMBER':
        shape[field.name] = getNumberValidator(field);
        break;
      case 'DROPDOWN':
        shape[field.name] = getDropdownValidator(field);
        break;
      case 'MULTISELECT':
        shape[field.name] = getMultiSelectValidator(field);
        break;
      case 'DATEPICKER':
        shape[field.name] = getDatePickerValidator(field);
        break;
      case 'FILE':
        shape[field.name] = getFileValidator(field);
        break;
      // LABEL: no validation
      default:
        break;
    }
  });

  return yup.object().shape(shape);
}
