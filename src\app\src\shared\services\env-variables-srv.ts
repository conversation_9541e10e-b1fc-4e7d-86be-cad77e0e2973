import { removeTrailingSlash } from 'shared/utils/urlUtils';

type EnvironmentVariable =
  | 'CENFORGE_API_URL'
  | 'CENFORGE_PUBLIC_URL'
  | 'LOCAL_TENANT'
  | 'LOCAL_TOKEN'
  | 'AXON_SHARED_LOCAL_COOKIE'
  | 'AXON_SHARED_LOCAL_USER'
  | 'AXON_CORE_API_BASE_URL';

export default {
  getVariable: (variable: EnvironmentVariable): string => {
    return (window as any)._env_[variable];
  },
  getApiBaseUrl: (): string => {
    return removeTrailingSlash(window._env_['CENFORGE_API_URL']);
  },
  getAxonCoreApiBaseUrl: (): string => {
    return removeTrailingSlash(window._env_.AXON_CORE_API_BASE_URL);
  },
};
