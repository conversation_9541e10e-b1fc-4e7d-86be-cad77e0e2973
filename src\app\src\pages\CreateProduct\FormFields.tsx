import React from 'react';

import { t } from 'i18next';
import { PhlexLayout, PhlexInput, PhlexDropdown, PhlexMultiSelect, PhlexDatePicker, PhlexUpload, PhlexChipList } from 'phlex-core-ui';
import { ChipItemProps } from 'phlex-core-ui/build/src/controls/PhlexChipList/PhlexChipList';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { useFormContext, Controller } from 'react-hook-form';

import StyledComponents from './CreateProduct.styles';

export type FieldType = {
  name: string;
  displayName: string;
  inputType: string;
  options?: IOption[] | string[];
  value?: string;
  fields?: FieldType[];
  validate?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    regEx?: string;
    regExMessage?: string;
    allowedExtensions?: string;
  };
};

export const FormFields = ({ field }: { field: FieldType }): JSX.Element => {
  const { StyledSectionWrapper } = StyledComponents;
  const { InputLabel, SectionHeading, SectionSubheading } = PhlexLayout;

  const {
    control,
    formState: { errors },
    trigger,
  } = useFormContext();

  switch (field.inputType) {
    case 'TEXT':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={{
            required: field.validate?.required,
            minLength: field.validate?.minLength,
            maxLength: field.validate?.maxLength,
          }}
          render={({ field: controllerField }) => (
            <PhlexInput
              key={field.name}
              name={field.name}
              required={field.validate?.required}
              label={field.displayName}
              width="fullwidth"
              value={controllerField.value ?? ''}
              onChange={(e) => {
                controllerField.onChange(e);
                if (errors[field.name]) {
                  trigger(field.name);
                }
              }}
              onBlur={() => {
                controllerField.onBlur();
                trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      );
    case 'MULTITEXT':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={{
            required: field.validate?.required,
          }}
          render={({ field: controllerField }) => (
            <PhlexChipList
              key={field.name}
              name={field.name}
              required={field.validate?.required}
              heading={field.displayName}
              showAddButton
              disableClick
              data={controllerField.value}
              saveValidator={(text: string) => {
                if (field.validate?.regEx && !new RegExp(field.validate.regEx).test(text)) {
                  return field.validate.regExMessage;
                }
                if (field.validate?.minLength && text.length < field.validate.minLength) {
                  return t('DataScreen.Forms.InsertMinCharacters', { number: field.validate.minLength.toString() });
                }
                if (field.validate?.maxLength && text.length > field.validate.maxLength) {
                  return t('DataScreen.Forms.InsertMaxCharacters', { number: field.validate.maxLength.toString() });
                }
                return undefined;
              }}
              saveHandler={(chip: ChipItemProps) => {
                const newValue = [...(controllerField.value ?? []), chip];
                controllerField.onChange(newValue);
                trigger(field.name);
              }}
              deleteHandler={(chip: ChipItemProps) => {
                const newValue = (controllerField.value ?? []).filter((c: ChipItemProps) => c.id !== chip.id);
                controllerField.onChange(newValue);
                trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      );
    case 'NUMBER':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={{
            required: field.validate?.required,
          }}
          render={({ field: controllerField }) => (
            <PhlexInput
              key={field.name}
              name={field.name}
              required={field.validate?.required}
              label={field.displayName}
              type="number"
              width="fullwidth"
              value={controllerField.value ?? ''}
              onChange={(e) => {
                controllerField.onChange(e);
                if (errors[field.name]) {
                  trigger(field.name);
                }
              }}
              onBlur={() => {
                controllerField.onBlur();
                trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      );
    case 'DROPDOWN':
      return (
        <Controller
          name={field.name}
          control={control}
          rules={{ required: field.validate?.required }}
          render={({ field: controllerField }) => (
            <PhlexDropdown
              key={field.name}
              name={field.name}
              required={field.validate?.required}
              label={field.displayName}
              data={field.options}
              width="fullwidth"
              value={controllerField.value}
              onChange={(e) => {
                controllerField.onChange(e);
                if (errors[field.name]) {
                  trigger(field.name);
                }
              }}
              onBlur={() => {
                controllerField.onBlur();
                trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      );
    case 'MULTISELECT':
      return (
        <Controller
          name={field.name}
          control={control}
          render={({ field: controllerField }) => (
            <PhlexMultiSelect
              key={field.name}
              name={field.name}
              label={field.displayName}
              data={field.options}
              width="fullwidth"
              value={controllerField.value}
              onChange={(e) => {
                controllerField.onChange(e);
                if (errors[field.name]) {
                  trigger(field.name);
                }
              }}
              onBlur={() => {
                controllerField.onBlur();
                trigger(field.name);
              }}
              fetchData={function (..._args: unknown[]): Promise<IOption[]> {
                return Promise.resolve(field.options as IOption[]);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      );
    case 'DATEPICKER':
      return (
        <Controller
          name={field.name}
          control={control}
          render={({ field: controllerField }) => (
            <PhlexDatePicker
              key={field.name}
              name={field.name}
              label={field.displayName}
              required={field.validate?.required}
              value={controllerField.value}
              onChange={controllerField.onChange}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      );
    case 'FILE':
      return (
        <Controller
          name={field.name}
          control={control}
          render={() => (
            <div key={field.name}>
              <InputLabel htmlFor={field.name}>{field.displayName}</InputLabel>
              <PhlexUpload
                name={field.name}
                uploadUrl={undefined}
                maxFileNameLength={255}
                fileTypes={field.validate?.allowedExtensions}
                maxFilesPerUpload={1}
                feedbackMessages={{
                  removeInvalid: '',
                  restrictions: 'File restrictions.',
                  tooMany: '',
                }}
                statusMessages={{
                  uploadFailed: 'Upload failed',
                  initial: 'No file selected',
                  selected: 'File selected',
                  uploading: 'Uploading...',
                  uploaded: 'Upload complete',
                  removeFailed: 'Remove failed',
                  removing: 'Removing...',
                  invalidFileExtension: 'Invalid file type',
                  invalidMaxFileSize: 'File is too large',
                  invalidMinFileSize: 'File is too small',
                  invalidEmptyName: 'File name is required',
                  invalidNameTooLong: 'File name is too long',
                }}
                tooltipText={{
                  remove: 'Remove file',
                }}
              />
            </div>
          )}
        />
      );
    case 'LABEL':
      return (
        <StyledSectionWrapper key={field.name}>
          <SectionHeading>{field.displayName}</SectionHeading>
          <SectionSubheading>{field.value}</SectionSubheading>
        </StyledSectionWrapper>
      );
    default:
      return <></>;
  }
};
