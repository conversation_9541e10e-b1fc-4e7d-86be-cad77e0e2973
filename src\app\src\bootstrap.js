import React from 'react';

import App from 'App';
import { LocalNavigation } from 'components';
import { PhlexSharedThemeProvider } from 'phlex-core-ui';
import * as ReactDOMClient from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import setupAxiosInterceptors from 'shared/services/auth/axios-interceptor';

import { GlobalStyles } from './App.styles';

import './multilingual/i18n.ts';
import '@progress/kendo-theme-default/dist/all.css';

setupAxiosInterceptors();

const container = document.getElementById('root');
const root = ReactDOMClient.createRoot(container);

root.render(
  <BrowserRouter>
    <PhlexSharedThemeProvider>
      <GlobalStyles />
      <LocalNavigation />
      <App />
    </PhlexSharedThemeProvider>
  </BrowserRouter>
);
