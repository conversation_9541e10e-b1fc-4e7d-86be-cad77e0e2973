import React, { ReactNode, useState } from 'react';

import { t } from 'i18next';
import { routes } from 'pages/routes';
import { PhlexButton, PhlexLayout } from 'phlex-core-ui';
import { useNavigate } from 'react-router-dom';
import { BasePathType } from 'types/path';

import { Breadcrumbs } from './getProductsBreadcrumbs';
import { ProductsContent } from './ProductsContent';

export const Products = ({ basepath }: BasePathType): ReactNode => {
  const navigate = useNavigate();
  const { PageActions, PageTopBar } = PhlexLayout;

  return (
    <>
      <PageTopBar>
        <Breadcrumbs />
        <PageActions>
          <PhlexButton label={t('Product.Buttons.Create')} iconName="add" onClick={() => navigate(`${basepath}${routes.createProduct}`)} />
        </PageActions>
      </PageTopBar>
      <ProductsContent basepath={basepath} />
    </>
  );
};
