import React, { forwardRef, useEffect, useImperativeHandle } from 'react';

import { useForm, FormProvider } from 'react-hook-form';

import StyledComponents from './CreateProduct.styles';

export type SummaryProps = {
  defaultValues?: Record<string, unknown>;
  onFormChange?: (values: Record<string, unknown>) => void;
};

const Summary = forwardRef<unknown, SummaryProps>(({ defaultValues, onFormChange }, ref) => {
  const { ProductForm } = StyledComponents;

  const methods = useForm({
    defaultValues: defaultValues || { documentname: '', description: '' },
  });

  useEffect(() => {
    if (defaultValues) {
      methods.reset(defaultValues);
    }
  }, [defaultValues]);

  useImperativeHandle(ref, () => ({
    trigger: methods.trigger,
    getValues: methods.getValues,
  }));

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = (data: Record<string, unknown>): void => {
    console.log('Summary form submitted:', data);
  };

  // Watch all form values and notify parent on change
  React.useEffect(() => {
    if (onFormChange) {
      const subscription = methods.watch((values: Record<string, unknown>) => {
        onFormChange(values);
      });
      return () => subscription.unsubscribe();
    }
  }, [methods, onFormChange]);

  return (
    <FormProvider {...methods}>
      <ProductForm onSubmit={handleSubmit(onSubmit)}>Summary content here</ProductForm>
    </FormProvider>
  );
});

Summary.displayName = 'Summary';

export { Summary };
