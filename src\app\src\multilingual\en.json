{"Nav": {"Home": "Home", "Products": "Products", "CreateProduct": "Create Product"}, "CreateProduct": {"StepTitles": {"Services": "Services", "Summary": "Summary"}, "NewProduct": "New product", "ProductOverview": {"SectionTitle": "Product overview", "SectionSubTitle": "Provide the product name, short name, and a brief description to identify the product.", "ProductName": "Product Name", "ShortName": "Short Name", "Description": "Description"}, "ProductManagement": {"SectionTitle": "Product Management", "SectionSubTitle": "Streamline project initiation by defining ownership, establishing workspaces, and adding supporting tools.", "ProductOwner": "Product Owner", "OwnerTeam": "Owner Team", "CreateJiraProject": "Create JIRA Project?", "JiraProjectCategory": "JIRA Project Category", "CreateConfluencePage": "Create Confluence Page?"}, "Infrastructure": {"SectionTitle": "Infrastructure", "SectionSubTitle": "Use this section to manage domain selection and link the necessary resources for your application.", "AzureDomain": "Domain", "AzureResources": {"Title": "Optional Azure Resources", "Text": "Select which resources you will need for your app."}}, "Services": {"SectionTitle": "Services", "SectionSubTitle": "Easily define and customise services to match your project's requirements.", "ServiceType": "Service Type", "ServiceName": "Service Name", "ApiName": "API Name", "ServiceOptions": "Service Options", "Description": "Description", "AddService": "Add a Service", "RemoveService": "Remove service", "ServiceTypes": {"Web": "Web Application", "API": "API Service", "Database": "Database", "Queue": "Message Queue", "Cache": "Cache Service", "Storage": "Storage Service", "Microservice": "Microservice", "Function": "Serverless Function"}}}, "Product": {"Buttons": {"Create": "Create Product"}, "PageNames": {"ViewProducts": "Products"}}, "Shared": {"Back": "Back", "Next": "Next", "Cancel": "Cancel", "Submit": "Submit"}, "DataScreen": {"Forms": {"InvalidCharacters": "The character(s) {{characterList}} are not allowed.", "RequiredField": "{{field}} is required.", "ValidField": "A valid {{field}} is required.", "InsertMaxCharacters": "Maximum of {{number}} characters.", "InsertMinCharacters": "Minimum of {{number}} characters.", "Fields": {"EmailAddress": "Email Address"}}}}