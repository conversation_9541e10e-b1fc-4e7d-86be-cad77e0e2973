import fs from 'fs';

import CopyWebpackPlugin from 'copy-webpack-plugin';
import ESLintPlugin from 'eslint-webpack-plugin';
import ForkTsCheckerWebpackPlugin from 'fork-ts-checker-webpack-plugin';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';

import paths from './config/paths';

const { ModuleFederationPlugin } = require('webpack').container;

const deps = require('./package.json').dependencies;

module.exports = () => {
  return {
    entry: [paths.appIndexJs],
    mode: 'development',
    devtool: 'inline-source-map',
    devServer: {
      static: paths.appBuild,
      historyApiFallback: true,
      port: 4070,
      open: true,
      hot: true,
      server: {
        type: 'https',
        options: {
          key: fs.readFileSync('./ssl/key.pem'),
          cert: fs.readFileSync('./ssl/cert.pem'),
        },
      },
    },
    output: {
      publicPath: 'auto',
    },
    module: {
      rules: [
        {
          test: /\.(ts|js)x?$/i,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
            },
          },
        },
        {
          test: /\.css?$/i,
          use: ['style-loader', 'css-loader'],
        },
      ],
    },
    resolve: {
      plugins: [new TsconfigPathsPlugin()],
      extensions: ['.tsx', '.ts', '.js', '.jsx'],
    },
    plugins: [
      new ModuleFederationPlugin({
        name: 'cenforge',
        filename: 'remoteEntry.js',
        exposes: {
          './App': './src/App',
          './Navigation': './src/Navigation',
        },
        shared: {
          react: {
            import: 'react', // the "react" package will be used a provided and fallback module
            shareKey: 'react', // under this name the shared module will be placed in the share scope
            shareScope: 'default', // share scope with this name will be used
            singleton: true,
            requiredVersion: deps['react'],
          },
          'react-dom': {
            singleton: true,
            requiredVersion: deps['react-dom'],
          },
          'styled-components': {
            singleton: true,
          },
          'react-router': {
            singleton: true,
          },
          'react-router-dom': {
            singleton: true,
          },
        },
      }),
      new HtmlWebpackPlugin({
        template: paths.appHtml,
      }),
      new ForkTsCheckerWebpackPlugin({
        async: false,
      }),
      new ESLintPlugin({
        extensions: ['js', 'jsx', 'ts', 'tsx'],
        failOnError: true,
        failOnWarning: false,
        emitError: true,
        emitWarning: false,
      }),
      new CopyWebpackPlugin({
        patterns: [
          { from: './assets', to: 'assets' },
          { from: './env-config.js', to: '' },
        ],
      }),
    ],
  };
};
