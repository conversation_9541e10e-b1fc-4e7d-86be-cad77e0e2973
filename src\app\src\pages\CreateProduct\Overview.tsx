import React, { forwardRef, useEffect, useImperativeHandle } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { PhlexInput, PhlexLayout, PhlexTextArea } from 'phlex-core-ui';
import { Controller, FormProvider, useForm } from 'react-hook-form';

import StyledComponents from './CreateProduct.styles';
import { buildYupSchema } from './yupSchema';

export type OverviewProps = {
  defaultValues?: Record<string, unknown>;
  onFormChange?: (values: Record<string, unknown>) => void;
};

const Overview = forwardRef<unknown, OverviewProps>(({ defaultValues, onFormChange }, ref) => {
  const { SectionHeading, SectionSubheading } = PhlexLayout;
  const { ProductForm, StyledSectionWrapper } = StyledComponents;

  const fields = [
    {
      name: 'productname',
      displayName: t('CreateProduct.ProductOverview.ProductName'),
      inputType: 'TEXT',
      validate: {
        required: true,
        minLength: 1,
        maxLength: 256,
        regEx: '^[^\\\\/:*?"<>;#${},+=\\[\\]|]*$', // need double escaped characters
        regExMessage: 'Use of illegal characters: \\ / : * ? " < > ; # $ * { } , + = [ ] |',
      },
    },
    {
      name: 'shortname',
      displayName: t('CreateProduct.ProductOverview.ShortName'),
      inputType: 'TEXT',
      validate: {
        required: true,
        minLength: 2,
        maxLength: 7,
        regEx: '^(?!PRD)[A-Z]+$',
        regExMessage: '2-7 uppercase letters, not starting with "PRD"',
      },
    },
    {
      name: 'description',
      displayName: t('CreateProduct.ProductOverview.Description'),
      inputType: 'TEXT',
      validate: { required: false, maxLength: 500 },
    },
  ];

  const schema = React.useMemo(() => buildYupSchema(fields), [t]);

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues: defaultValues || { productname: '', description: '' },
  });

  useEffect(() => {
    if (defaultValues) {
      methods.reset(defaultValues);
    }
  }, [defaultValues]);

  useImperativeHandle(ref, () => ({
    trigger: methods.trigger,
    getValues: methods.getValues,
  }));

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = (data: Record<string, unknown>): void => {
    console.log('Overview form submitted:', data);
  };

  // Watch all form values and notify parent on change
  React.useEffect(() => {
    if (onFormChange) {
      const subscription = methods.watch((values: Record<string, unknown>) => {
        onFormChange(values);
      });
      return () => subscription.unsubscribe();
    }
  }, [methods, onFormChange]);

  return (
    <FormProvider {...methods}>
      <ProductForm onSubmit={handleSubmit(onSubmit)}>
        <StyledSectionWrapper>
          <SectionHeading>{t('CreateProduct.ProductOverview.SectionTitle')}</SectionHeading>
          <SectionSubheading>{t('CreateProduct.ProductOverview.SectionSubTitle')}</SectionSubheading>
        </StyledSectionWrapper>

        <Controller
          name="productname"
          control={control}
          render={({ field }) => (
            <PhlexInput
              name={field.name}
              value={typeof field.value === 'string' ? field.value : ''}
              onChange={(e) => {
                field.onChange(e);
                methods.trigger('productname');
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger('productname');
              }}
              label={t('CreateProduct.ProductOverview.ProductName')}
              required
              width="fullwidth"
              validationMessage={errors.productname?.message as string}
            />
          )}
        />
        <Controller
          name="shortname"
          control={control}
          render={({ field }) => (
            <PhlexInput
              name={field.name}
              value={typeof field.value === 'string' ? field.value : ''}
              onChange={(e) => {
                field.onChange(e);
                methods.trigger('shortname');
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger('shortname');
              }}
              label={t('CreateProduct.ProductOverview.ShortName')}
              required
              width="fullwidth"
              validationMessage={errors.shortname?.message as string}
            />
          )}
        />
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <PhlexTextArea
              name={field.name}
              value={typeof field.value === 'string' ? field.value : ''}
              onChange={(e) => {
                field.onChange(e);
                methods.trigger('description');
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger('description');
              }}
              label={t('CreateProduct.ProductOverview.Description')}
              width="fullwidth"
              validationMessage={errors.description?.message as string}
            />
          )}
        />
      </ProductForm>
    </FormProvider>
  );
});

Overview.displayName = 'Overview';

export { Overview };
