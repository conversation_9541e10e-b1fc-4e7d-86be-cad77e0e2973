import React, { forwardRef, useEffect, useImperativeHandle } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { PhlexDropdown, PhlexLayout, PhlexMultiSelect } from 'phlex-core-ui';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { Controller, FormProvider, useForm } from 'react-hook-form';

import StyledComponents from './CreateProduct.styles';
import { buildYupSchema } from './yupSchema';

export type ManagementProps = {
  defaultValues?: Record<string, unknown>;
  onFormChange?: (values: Record<string, unknown>) => void;
};

const Management = forwardRef<unknown, ManagementProps>(({ defaultValues, onFormChange }, ref) => {
  const { SectionHeading, SectionSubheading } = PhlexLayout;
  const { ProductForm, StyledSectionWrapper } = StyledComponents;

  const fields = [
    {
      name: 'productowner',
      displayName: t('CreateProduct.ProductManagement.ProductOwner'),
      inputType: 'DROPDOWN',
      options: ['Product Owner 1', 'Product Owner 2', 'Product Owner 3', 'Product Owner 4'],
      validate: { required: true },
    },
    {
      name: 'ownerteam',
      displayName: t('CreateProduct.ProductManagement.OwnerTeam'),
      inputType: 'MULTISELECT',
      options: [
        'Automation Team',
        'Code Brewers',
        'Code Busters',
        'Team Excelsior',
        'Team Fabulous',
        'Team Marvel',
        'Team MEME',
        'Team Maverick',
        'Team Ultimate',
      ],
      validate: { required: true },
    },
    {
      name: 'createjiraproject',
      displayName: t('CreateProduct.ProductManagement.CreateJiraProject'),
      inputType: 'DROPDOWN',
      options: ['Yes', 'No'],
      validate: { required: true },
    },
    {
      name: 'jiraprojectcategory',
      displayName: t('CreateProduct.ProductManagement.JiraProjectCategory'),
      inputType: 'DROPDOWN',
      options: ['Category 1', 'Category 2', 'Category 3', 'Category 4'],
      validate: { required: true },
    },
    {
      name: 'createconfluencepage',
      displayName: t('CreateProduct.ProductManagement.CreateConfluencePage'),
      inputType: 'DROPDOWN',
      options: ['Yes', 'No'],
      validate: { required: true },
    },
  ];

  const schema = React.useMemo(() => buildYupSchema(fields), [t]);

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues: defaultValues || { documentname: '', description: '' },
  });

  useEffect(() => {
    if (defaultValues) {
      methods.reset(defaultValues);
    }
  }, [defaultValues]);

  useImperativeHandle(ref, () => ({
    trigger: methods.trigger,
    getValues: methods.getValues,
  }));

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = methods;

  const onSubmit = (data: Record<string, unknown>): void => {
    console.log('Product Management form submitted:', data);
  };

  // Watch all form values and notify parent on change
  React.useEffect(() => {
    if (onFormChange) {
      const subscription = methods.watch((values: Record<string, unknown>) => {
        onFormChange(values);
      });
      return () => subscription.unsubscribe();
    }
  }, [methods, onFormChange]);

  return (
    <FormProvider {...methods}>
      <ProductForm onSubmit={handleSubmit(onSubmit)}>
        <StyledSectionWrapper>
          <SectionHeading>{t('CreateProduct.ProductManagement.SectionTitle')}</SectionHeading>
          <SectionSubheading>{t('CreateProduct.ProductManagement.SectionSubTitle')}</SectionSubheading>
        </StyledSectionWrapper>

        <Controller
          name="productowner"
          control={control}
          render={({ field }) => (
            <PhlexDropdown
              key={field.name}
              name={field.name}
              required
              label={t('CreateProduct.ProductManagement.ProductOwner')}
              data={fields[0].options}
              width="fullwidth"
              value={field.value}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />

        <Controller
          name="ownerteam"
          control={control}
          render={({ field }) => (
            <PhlexMultiSelect
              key={field.name}
              name={field.name}
              label={t('CreateProduct.ProductManagement.OwnerTeam')}
              data={fields[1].options}
              width="fullwidth"
              required
              value={field.value as IOption[]}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              fetchData={function (..._args: unknown[]): Promise<IOption[]> {
                return Promise.resolve(fields[1].options.map((option) => ({ value: option, label: option })) as IOption[]);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />

        <Controller
          name="createjiraproject"
          control={control}
          render={({ field }) => (
            <PhlexDropdown
              key={field.name}
              name={field.name}
              required
              label={t('CreateProduct.ProductManagement.CreateJiraProject')}
              data={fields[2].options}
              width="fullwidth"
              value={field.value}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />

        <Controller
          name="jiraprojectcategory"
          control={control}
          render={({ field }) => (
            <PhlexDropdown
              key={field.name}
              name={field.name}
              required
              label={t('CreateProduct.ProductManagement.JiraProjectCategory')}
              data={fields[3].options}
              width="fullwidth"
              value={field.value}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />

        <Controller
          name="createconfluencepage"
          control={control}
          render={({ field }) => (
            <PhlexDropdown
              key={field.name}
              name={field.name}
              required
              label={t('CreateProduct.ProductManagement.CreateConfluencePage')}
              data={fields[4].options}
              width="fullwidth"
              value={field.value}
              onChange={(e) => {
                field.onChange(e);
                if (errors[field.name]) {
                  methods.trigger(field.name);
                }
              }}
              onBlur={() => {
                field.onBlur();
                methods.trigger(field.name);
              }}
              validationMessage={errors[field.name]?.message as string}
            />
          )}
        />
      </ProductForm>
    </FormProvider>
  );
});

Management.displayName = 'Management';

export { Management };
