import React, { ReactNode } from 'react';

import { CreateProduct } from 'pages/CreateProduct/CreateProduct';
import { Products } from 'pages/Products';
import { routes } from 'pages/routes';
import { PhlexToastContainer } from 'phlex-core-ui';
import { Route, Routes } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import setupAxiosInterceptors from 'shared/services/auth/axios-interceptor';
import { removeTrailingSlash } from 'shared/utils/urlUtils';
import { BasePathType } from 'types/path';

const AppRoutes = ({ basepath }: BasePathType): ReactNode => {
  const cleanBasePath = removeTrailingSlash(basepath ?? '');

  setupAxiosInterceptors();

  return (
    <>
      <Routes>
        <Route path={routes.products} element={<Products basepath={cleanBasePath} />} />
        <Route path={routes.createProduct} element={<CreateProduct basepath={cleanBasePath} />} />
      </Routes>
      <ToastContainer />
      <PhlexToastContainer />
    </>
  );
};

export default AppRoutes;
