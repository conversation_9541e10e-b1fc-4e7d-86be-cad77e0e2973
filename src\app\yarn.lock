# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@^4.4.0":
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@adobe/css-tools/-/css-tools-4.4.1.tgz"
  integrity sha1-JEeiML/gcsFlnmgVEpwDzxcHEOM=

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@asamuzakjp/css-color@^3.2.0":
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@asamuzakjp/css-color/-/css-color-3.2.0.tgz"
  integrity sha1-zEL1uFxZP3nx+k8l0rmzIeYdF5Q=
  dependencies:
    "@csstools/css-calc" "^2.1.3"
    "@csstools/css-color-parser" "^3.0.9"
    "@csstools/css-parser-algorithms" "^3.0.4"
    "@csstools/css-tokenizer" "^3.0.3"
    lru-cache "^10.4.3"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2", "@babel/compat-data@^7.27.7", "@babel/compat-data@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/compat-data/-/compat-data-7.28.0.tgz"
  integrity sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=

"@babel/core@^7.0.0", "@babel/core@^7.0.0 || ^8.0.0-0", "@babel/core@^7.0.0-0", "@babel/core@^7.0.0-0 || ^8.0.0-0 <8.0.0", "@babel/core@^7.11.0", "@babel/core@^7.12.0", "@babel/core@^7.13.0", "@babel/core@^7.23.9", "@babel/core@^7.27.4", "@babel/core@^7.28.0", "@babel/core@^7.4.0 || ^8.0.0-0 <8.0.0", "@babel/core@>=7.0.0-beta.0 <8":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/core/-/core-7.28.3.tgz"
  integrity sha1-rO3d5pxdHe9puDnQnvo+P/Wcl8s=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.28.3"
    "@babel/helpers" "^7.28.3"
    "@babel/parser" "^7.28.3"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.3"
    "@babel/types" "^7.28.2"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.27.5", "@babel/generator@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/generator/-/generator-7.28.3.tgz"
  integrity sha1-libBdBxlDLrDkSFpSg8tdFG47z4=
  dependencies:
    "@babel/parser" "^7.28.3"
    "@babel/types" "^7.28.2"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1", "@babel/helper-annotate-as-pure@^7.27.3":
  version "7.27.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz"
  integrity sha1-8x/Ya5FfxNrx86xpdsWb5whO2cU=
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/helper-compilation-targets@^7.27.1", "@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.27.1", "@babel/helper-create-class-features-plugin@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.28.3.tgz"
  integrity sha1-PnR0NOoAeRDDIMTTmmtG8g83HUY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.28.3"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz"
  integrity sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.5":
  version "0.6.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.5.tgz"
  integrity sha1-dCzPHLADwHtIhZ/J+iwbvkDl91M=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    debug "^4.4.1"
    lodash.debounce "^4.0.8"
    resolve "^1.22.10"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-globals/-/helper-globals-7.28.0.tgz"
  integrity sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz"
  integrity sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.1", "@babel/helper-module-transforms@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz"
  integrity sha1-orN9PaOyNE/ghdqyNEJvK5ovpfY=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz"
  integrity sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-remap-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz"
  integrity sha1-RgHVx84usq6lgyjUNyVSP802LOY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-wrap-function" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz"
  integrity sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz"
  integrity sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helper-wrap-function@^7.27.1":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helper-wrap-function/-/helper-wrap-function-7.28.3.tgz"
  integrity sha1-/khyCSvBQ4/9DOV55vaZYJ+dCno=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.3"
    "@babel/types" "^7.28.2"

"@babel/helpers@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/helpers/-/helpers-7.28.3.tgz"
  integrity sha1-uDFWwKIjLBM9G1Nd1dNFIRnH5EE=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.2"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.27.2", "@babel/parser@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/parser/-/parser-7.28.3.tgz"
  integrity sha1-0tJbgUYhvKX+nRcryTeSVH56KnE=
  dependencies:
    "@babel/types" "^7.28.2"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz"
  integrity sha1-Yd2KjmH361aCaNG18SnaPu42S/k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz"
  integrity sha1-Q/cKbX79UjcO7731WuA9kbKThW0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz"
  integrity sha1-vrYjvVc7i28wR70EwyUGrcPlinI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz"
  integrity sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.28.3.tgz"
  integrity sha1-Nz9uLeABb3PK+PJwBPYdFndDdCo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha1-eET5KJVG76n+usLeTP41igUL1wM=

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-assertions@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.27.1.tgz"
  integrity sha1-iIlK79KwO17mrRVip8jhWHSWrs0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-attributes@^7.24.7", "@babel/plugin-syntax-import-attributes@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz"
  integrity sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz"
  integrity sha1-biBhBnujqwJm2DSp+UgRGW8qupo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-async-generator-functions@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.28.0.tgz"
  integrity sha1-Enbmxyhass0ezLC8c1a3pp/4QsI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz"
  integrity sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"

"@babel/plugin-transform-block-scoped-functions@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.27.1.tgz"
  integrity sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-block-scoping@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.28.0.tgz"
  integrity sha1-58UMuswYA08hC5Pe+oljhmYJlFE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-properties@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz"
  integrity sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-static-block@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.28.3.tgz"
  integrity sha1-0bjmm1TJmTvFWCA+H0m/yXm/2FI=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.28.3"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-classes@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-classes/-/plugin-transform-classes-7.28.3.tgz"
  integrity sha1-WYKXJgND0O29UctfUHXgfe6Rljo=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-globals" "^7.28.0"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/plugin-transform-computed-properties@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz"
  integrity sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/template" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.28.0.tgz"
  integrity sha1-DxVliPacWWCJt9Wwb1r4PZqn+Xo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-dotall-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz"
  integrity sha1-qmgh3oZMUosf7PKG8KF0446Cb00=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-keys@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.27.1.tgz"
  integrity sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz"
  integrity sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dynamic-import@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.27.1.tgz"
  integrity sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-explicit-resource-management@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-explicit-resource-management/-/plugin-transform-explicit-resource-management-7.28.0.tgz"
  integrity sha1-Rb5iEbd42/S51UxOiitC+nLgmho=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"

"@babel/plugin-transform-exponentiation-operator@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.27.1.tgz"
  integrity sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-export-namespace-from@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz"
  integrity sha1-ccpp00ce3W2qcRz038NABBXfnCM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-for-of@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz"
  integrity sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-function-name@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz"
  integrity sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-json-strings@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.27.1.tgz"
  integrity sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-literals@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz"
  integrity sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-logical-assignment-operators@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz"
  integrity sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-member-expression-literals@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.27.1.tgz"
  integrity sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-amd@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.27.1.tgz"
  integrity sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz"
  integrity sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-systemjs@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.27.1.tgz"
  integrity sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-modules-umd@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.27.1.tgz"
  integrity sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz"
  integrity sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-new-target@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.27.1.tgz"
  integrity sha1-JZxDk5coytFwasFzUbfmp76hq+s=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-nullish-coalescing-operator@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz"
  integrity sha1-T50xU79ngtc91CeFqdItAxl7yR0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-numeric-separator@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz"
  integrity sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-rest-spread@^7.28.0":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.28.0.tgz"
  integrity sha1-0jAhhX/9fNgJ9U1iQpm4CGQC7Y0=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"
    "@babel/plugin-transform-parameters" "^7.27.7"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-object-super@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.27.1.tgz"
  integrity sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"

"@babel/plugin-transform-optional-catch-binding@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz"
  integrity sha1-hMc0Hr3jXM02sTfp5FhmglByoww=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz"
  integrity sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-parameters@^7.27.7":
  version "7.27.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.7.tgz"
  integrity sha1-H9L+u3x059Ic87BfeuvJB5QK9To=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-methods@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz"
  integrity sha1-/ay6scXtgexw39u4shPWXaFItq8=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-property-in-object@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz"
  integrity sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-property-literals@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.27.1.tgz"
  integrity sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-display-name@^7.27.1":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.28.0.tgz"
  integrity sha1-byCnKV/qffQutC/tj4loE/W5NN4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-development@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz"
  integrity sha1-R/+VlA4go6cOaK09T8tle2R/bJg=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.27.1"

"@babel/plugin-transform-react-jsx@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz"
  integrity sha1-ECO8lLeLCi1oyCtelq7Vc7z7nbA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/plugin-transform-react-pure-annotations@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.27.1.tgz"
  integrity sha1-M58c41Xq4kLgZJ8jKxxokHwC6Hk=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regenerator@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.28.3.tgz"
  integrity sha1-uO7g+K7TdwS7zJMv0LGgo00Lc0Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regexp-modifiers@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.27.1.tgz"
  integrity sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-reserved-words@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.27.1.tgz"
  integrity sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-runtime@^7.28.0":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.28.3.tgz"
  integrity sha1-9ZkKGy0r3pUO1JORXgcZhByNDqo=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    babel-plugin-polyfill-corejs2 "^0.4.14"
    babel-plugin-polyfill-corejs3 "^0.13.0"
    babel-plugin-polyfill-regenerator "^0.6.5"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz"
  integrity sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-spread@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz"
  integrity sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-sticky-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz"
  integrity sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-template-literals@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz"
  integrity sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typeof-symbol@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.1.tgz"
  integrity sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typescript@^7.27.1":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.28.0.tgz"
  integrity sha1-eWy9JJq1bBgWi0nj4dNBtyrwSms=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"

"@babel/plugin-transform-unicode-escapes@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.27.1.tgz"
  integrity sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-property-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.27.1.tgz"
  integrity sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz"
  integrity sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-sets-regex@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.27.1.tgz"
  integrity sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/preset-env@^7.28.0":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/preset-env/-/preset-env-7.28.3.tgz"
  integrity sha1-KxjZr/nmlkN4kFeuS5QrFlT4gYc=
  dependencies:
    "@babel/compat-data" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.28.3"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.27.1"
    "@babel/plugin-syntax-import-attributes" "^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.27.1"
    "@babel/plugin-transform-async-generator-functions" "^7.28.0"
    "@babel/plugin-transform-async-to-generator" "^7.27.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.27.1"
    "@babel/plugin-transform-block-scoping" "^7.28.0"
    "@babel/plugin-transform-class-properties" "^7.27.1"
    "@babel/plugin-transform-class-static-block" "^7.28.3"
    "@babel/plugin-transform-classes" "^7.28.3"
    "@babel/plugin-transform-computed-properties" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"
    "@babel/plugin-transform-dotall-regex" "^7.27.1"
    "@babel/plugin-transform-duplicate-keys" "^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-dynamic-import" "^7.27.1"
    "@babel/plugin-transform-explicit-resource-management" "^7.28.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.27.1"
    "@babel/plugin-transform-export-namespace-from" "^7.27.1"
    "@babel/plugin-transform-for-of" "^7.27.1"
    "@babel/plugin-transform-function-name" "^7.27.1"
    "@babel/plugin-transform-json-strings" "^7.27.1"
    "@babel/plugin-transform-literals" "^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.27.1"
    "@babel/plugin-transform-member-expression-literals" "^7.27.1"
    "@babel/plugin-transform-modules-amd" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-modules-systemjs" "^7.27.1"
    "@babel/plugin-transform-modules-umd" "^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-new-target" "^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.27.1"
    "@babel/plugin-transform-numeric-separator" "^7.27.1"
    "@babel/plugin-transform-object-rest-spread" "^7.28.0"
    "@babel/plugin-transform-object-super" "^7.27.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.7"
    "@babel/plugin-transform-private-methods" "^7.27.1"
    "@babel/plugin-transform-private-property-in-object" "^7.27.1"
    "@babel/plugin-transform-property-literals" "^7.27.1"
    "@babel/plugin-transform-regenerator" "^7.28.3"
    "@babel/plugin-transform-regexp-modifiers" "^7.27.1"
    "@babel/plugin-transform-reserved-words" "^7.27.1"
    "@babel/plugin-transform-shorthand-properties" "^7.27.1"
    "@babel/plugin-transform-spread" "^7.27.1"
    "@babel/plugin-transform-sticky-regex" "^7.27.1"
    "@babel/plugin-transform-template-literals" "^7.27.1"
    "@babel/plugin-transform-typeof-symbol" "^7.27.1"
    "@babel/plugin-transform-unicode-escapes" "^7.27.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.27.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.14"
    babel-plugin-polyfill-corejs3 "^0.13.0"
    babel-plugin-polyfill-regenerator "^0.6.5"
    core-js-compat "^3.43.0"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/preset-react/-/preset-react-7.27.1.tgz"
  integrity sha1-huoKXKOYRmP3RL4v0my2dHw/0Ow=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-transform-react-display-name" "^7.27.1"
    "@babel/plugin-transform-react-jsx" "^7.27.1"
    "@babel/plugin-transform-react-jsx-development" "^7.27.1"
    "@babel/plugin-transform-react-pure-annotations" "^7.27.1"

"@babel/preset-typescript@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz"
  integrity sha1-GQdCpkKNKCMGZIpVsFKbVhSE+RI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-typescript" "^7.27.1"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.16.3", "@babel/runtime@^7.27.6", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/runtime/-/runtime-7.28.3.tgz"
  integrity sha1-dcUDS1W6hoEhZovl1bsxzGTm5ho=

"@babel/template@^7.27.1", "@babel/template@^7.27.2":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/template/-/template-7.27.2.tgz"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.28.0", "@babel/traverse@^7.28.3":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/traverse/-/traverse-7.28.3.tgz"
  integrity sha1-aRGhB5XSzOQ+xqKM/8RAzKJZNDQ=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.3"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.2"
    debug "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.28.2", "@babel/types@^7.4.4":
  version "7.28.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@babel/types/-/types-7.28.2.tgz"
  integrity sha1-2p2whWqaiOChOwGYgddRNYjPcSs=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@csstools/color-helpers@^5.1.0":
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@csstools/color-helpers/-/color-helpers-5.1.0.tgz"
  integrity sha1-EGxUyAjKv9GrTGAthQXuWEwplu8=

"@csstools/css-calc@^2.1.3", "@csstools/css-calc@^2.1.4":
  version "2.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@csstools/css-calc/-/css-calc-2.1.4.tgz"
  integrity sha1-hHP2Pi/NbkWYON1BJAHVlI8iTGU=

"@csstools/css-color-parser@^3.0.9":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@csstools/css-color-parser/-/css-color-parser-3.1.0.tgz"
  integrity sha1-Tjhq86md02xG/vATz+TBw0Hu1vA=
  dependencies:
    "@csstools/color-helpers" "^5.1.0"
    "@csstools/css-calc" "^2.1.4"

"@csstools/css-parser-algorithms@^3.0.4", "@csstools/css-parser-algorithms@^3.0.5":
  version "3.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.5.tgz"
  integrity sha1-V1U3Cpopq67FUVtDyLPyz5wuMHY=

"@csstools/css-tokenizer@^3.0.3", "@csstools/css-tokenizer@^3.0.4":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@csstools/css-tokenizer/-/css-tokenizer-3.0.4.tgz"
  integrity sha1-Mz/tq8P9Go5dAQABNzHPGeaoxdM=

"@discoveryjs/json-ext@^0.6.1":
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@discoveryjs/json-ext/-/json-ext-0.6.3.tgz"
  integrity sha1-8Tx8IFkV65GuVMVX9ekr3di+DoM=

"@emnapi/core@^1.4.3":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@emnapi/core/-/core-1.5.0.tgz"
  integrity sha1-hc2EU37Jic67I0Ngah7mY85O2vA=
  dependencies:
    "@emnapi/wasi-threads" "1.1.0"
    tslib "^2.4.0"

"@emnapi/runtime@^1.4.3":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@emnapi/runtime/-/runtime-1.5.0.tgz"
  integrity sha1-muv8ubFxldzjq1PIZ4emt9BY23M=
  dependencies:
    tslib "^2.4.0"

"@emnapi/wasi-threads@1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@emnapi/wasi-threads/-/wasi-threads-1.1.0.tgz"
  integrity sha1-YLIQL93JzLeGB+Sjz4QD6mm+Qb8=
  dependencies:
    tslib "^2.4.0"

"@emotion/is-prop-valid@1.2.2":
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@emotion/is-prop-valid/-/is-prop-valid-1.2.2.tgz"
  integrity sha1-1BdQdmecaib6qSsDu3hvnlJhIzc=
  dependencies:
    "@emotion/memoize" "^0.8.1"

"@emotion/memoize@^0.8.1":
  version "0.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@emotion/memoize/-/memoize-0.8.1.tgz"
  integrity sha1-wd2wQEKcbSHTjMlF/nXIGM+2jhc=

"@emotion/unitless@0.8.1":
  version "0.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@emotion/unitless/-/unitless-0.8.1.tgz"
  integrity sha1-GCtaRwTvitkb3pP3qGCoj9kseaM=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.8.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.8.0.tgz"
  integrity sha1-DjteRVZtG84exH2Kri/CrXetCJQ=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.12.1":
  version "4.12.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/compat@^1.3.1":
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/compat/-/compat-1.3.2.tgz"
  integrity sha1-Godm5Ef609CRsaiLn3j4Z4Mihbc=

"@eslint/config-array@^0.21.0":
  version "0.21.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/config-array/-/config-array-0.21.0.tgz"
  integrity sha1-q9vL0WsSTGOAgXZjkqTWtQn3JjY=
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.3.1":
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/config-helpers/-/config-helpers-0.3.1.tgz"
  integrity sha1-0xbkeQW9ChqTH6UOZpua9BBNFhc=

"@eslint/core@^0.15.2":
  version "0.15.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/core/-/core-0.15.2.tgz"
  integrity sha1-WThjJ9eGLMNgPrx8eBWdLcxKho8=
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/eslintrc/-/eslintrc-3.3.1.tgz"
  integrity sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@^9.30.1", "@eslint/js@9.34.0":
  version "9.34.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/js/-/js-9.34.0.tgz"
  integrity sha1-/EIxaLnRDgjeqQiNCDeI7GRCmWs=

"@eslint/object-schema@^2.1.6":
  version "2.1.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/object-schema/-/object-schema-2.1.6.tgz"
  integrity sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=

"@eslint/plugin-kit@^0.3.5":
  version "0.3.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@eslint/plugin-kit/-/plugin-kit-0.3.5.tgz"
  integrity sha1-/Ydk8O55yN2rTaZUYMZBzv7gF8U=
  dependencies:
    "@eslint/core" "^0.15.2"
    levn "^0.4.1"

"@hookform/resolvers@^5.1.1":
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@hookform/resolvers/-/resolvers-5.2.1.tgz"
  integrity sha1-MzK0Zi/jAalprBt5XWY89ygykWY=
  dependencies:
    "@standard-schema/utils" "^0.3.0"

"@humanfs/core@^0.19.1":
  version "0.19.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@humanfs/core/-/core-0.19.1.tgz"
  integrity sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=

"@humanfs/node@^0.16.6":
  version "0.16.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@humanfs/node/-/node-0.16.6.tgz"
  integrity sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/retry@^0.3.0":
  version "0.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@humanwhocodes/retry/-/retry-0.3.1.tgz"
  integrity sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=

"@humanwhocodes/retry@^0.4.2":
  version "0.4.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@humanwhocodes/retry/-/retry-0.4.3.tgz"
  integrity sha1-wrnS43TuYsWG062+qHGZsdenpro=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@istanbuljs/schema/-/schema-0.1.3.tgz"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/console/-/console-30.1.2.tgz"
  integrity sha1-PTK5ZkVNV4dFILJ2RxKSKKZUyZU=
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    jest-message-util "30.1.0"
    jest-util "30.0.5"
    slash "^3.0.0"

"@jest/core@30.1.3":
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/core/-/core-30.1.3.tgz"
  integrity sha1-wJfc6tNqxszuKCWjUHgWNGX4t50=
  dependencies:
    "@jest/console" "30.1.2"
    "@jest/pattern" "30.0.1"
    "@jest/reporters" "30.1.3"
    "@jest/test-result" "30.1.3"
    "@jest/transform" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    ansi-escapes "^4.3.2"
    chalk "^4.1.2"
    ci-info "^4.2.0"
    exit-x "^0.2.2"
    graceful-fs "^4.2.11"
    jest-changed-files "30.0.5"
    jest-config "30.1.3"
    jest-haste-map "30.1.0"
    jest-message-util "30.1.0"
    jest-regex-util "30.0.1"
    jest-resolve "30.1.3"
    jest-resolve-dependencies "30.1.3"
    jest-runner "30.1.3"
    jest-runtime "30.1.3"
    jest-snapshot "30.1.2"
    jest-util "30.0.5"
    jest-validate "30.1.0"
    jest-watcher "30.1.3"
    micromatch "^4.0.8"
    pretty-format "30.0.5"
    slash "^3.0.0"

"@jest/diff-sequences@30.0.1":
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/diff-sequences/-/diff-sequences-30.0.1.tgz"
  integrity sha1-Dt7erk0HH1yP/jZ40V86G+CRVr4=

"@jest/environment-jsdom-abstract@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/environment-jsdom-abstract/-/environment-jsdom-abstract-30.1.2.tgz"
  integrity sha1-Tt5ia/J6UsIfEZF2CPSrU5NjsW8=
  dependencies:
    "@jest/environment" "30.1.2"
    "@jest/fake-timers" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/jsdom" "^21.1.7"
    "@types/node" "*"
    jest-mock "30.0.5"
    jest-util "30.0.5"

"@jest/environment@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/environment/-/environment-30.1.2.tgz"
  integrity sha1-8b1zp1cflhBKP/IAd0fCzhK1wDg=
  dependencies:
    "@jest/fake-timers" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    jest-mock "30.0.5"

"@jest/expect-utils@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/expect-utils/-/expect-utils-30.1.2.tgz"
  integrity sha1-iOoYBA9wfJ+ttv2ed1aMrlJmzug=
  dependencies:
    "@jest/get-type" "30.1.0"

"@jest/expect@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/expect/-/expect-30.1.2.tgz"
  integrity sha1-NSg+i9CDqrbMJtTTCu6stecZCg8=
  dependencies:
    expect "30.1.2"
    jest-snapshot "30.1.2"

"@jest/fake-timers@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/fake-timers/-/fake-timers-30.1.2.tgz"
  integrity sha1-yw32mVA01Qxpc//T/9qhNTqBbEE=
  dependencies:
    "@jest/types" "30.0.5"
    "@sinonjs/fake-timers" "^13.0.0"
    "@types/node" "*"
    jest-message-util "30.1.0"
    jest-mock "30.0.5"
    jest-util "30.0.5"

"@jest/get-type@30.1.0":
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/get-type/-/get-type-30.1.0.tgz"
  integrity sha1-T8tNwuvPCBG+HAT9HLecLbpDHLw=

"@jest/globals@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/globals/-/globals-30.1.2.tgz"
  integrity sha1-ghytfY7z3BRZeQiLsL+8H4Gl2M4=
  dependencies:
    "@jest/environment" "30.1.2"
    "@jest/expect" "30.1.2"
    "@jest/types" "30.0.5"
    jest-mock "30.0.5"

"@jest/pattern@30.0.1":
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/pattern/-/pattern-30.0.1.tgz"
  integrity sha1-1TBBR/SaBSkAtLhT3tsRHQgOGZ8=
  dependencies:
    "@types/node" "*"
    jest-regex-util "30.0.1"

"@jest/reporters@30.1.3":
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/reporters/-/reporters-30.1.3.tgz"
  integrity sha1-AVtYOLPt9g9umVGGzYBbf8ushrM=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "30.1.2"
    "@jest/test-result" "30.1.3"
    "@jest/transform" "30.1.2"
    "@jest/types" "30.0.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    "@types/node" "*"
    chalk "^4.1.2"
    collect-v8-coverage "^1.0.2"
    exit-x "^0.2.2"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^5.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "30.1.0"
    jest-util "30.0.5"
    jest-worker "30.1.0"
    slash "^3.0.0"
    string-length "^4.0.2"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/schemas/-/schemas-29.6.3.tgz"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/schemas@30.0.5":
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/schemas/-/schemas-30.0.5.tgz"
  integrity sha1-e99p/Fo2ilq9tJ/ZEDbFUiWEZHM=
  dependencies:
    "@sinclair/typebox" "^0.34.0"

"@jest/snapshot-utils@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/snapshot-utils/-/snapshot-utils-30.1.2.tgz"
  integrity sha1-MgUA66KaJcM+nsloFU5SGHNiQwk=
  dependencies:
    "@jest/types" "30.0.5"
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    natural-compare "^1.4.0"

"@jest/source-map@30.0.1":
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/source-map/-/source-map-30.0.1.tgz"
  integrity sha1-MF6+xQRo8T5liz1cJvhRB6ViCqo=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    callsites "^3.1.0"
    graceful-fs "^4.2.11"

"@jest/test-result@30.1.3":
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/test-result/-/test-result-30.1.3.tgz"
  integrity sha1-af5/+T2owMR7riRXJ+DOI1cdBY4=
  dependencies:
    "@jest/console" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/istanbul-lib-coverage" "^2.0.6"
    collect-v8-coverage "^1.0.2"

"@jest/test-sequencer@30.1.3":
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/test-sequencer/-/test-sequencer-30.1.3.tgz"
  integrity sha1-32QDjUYVDnBO0Hxf7kYmYJ9RgIk=
  dependencies:
    "@jest/test-result" "30.1.3"
    graceful-fs "^4.2.11"
    jest-haste-map "30.1.0"
    slash "^3.0.0"

"@jest/transform@^29.0.0 || ^30.0.0", "@jest/transform@30.1.2":
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/transform/-/transform-30.1.2.tgz"
  integrity sha1-QmJKnInyQnzUE7mJqvn2rrWMrlY=
  dependencies:
    "@babel/core" "^7.27.4"
    "@jest/types" "30.0.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    babel-plugin-istanbul "^7.0.0"
    chalk "^4.1.2"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.11"
    jest-haste-map "30.1.0"
    jest-regex-util "30.0.1"
    jest-util "30.0.5"
    micromatch "^4.0.8"
    pirates "^4.0.7"
    slash "^3.0.0"
    write-file-atomic "^5.0.1"

"@jest/types@^29.0.0 || ^30.0.0", "@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/types/-/types-29.6.3.tgz"
  integrity sha1-ETH4z2NOfoTF53urEvBSr1hfulk=
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jest/types@30.0.5":
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jest/types/-/types-30.0.5.tgz"
  integrity sha1-KaM6TANuOQTxz9lPb+d/idLhzAU=
  dependencies:
    "@jest/pattern" "30.0.1"
    "@jest/schemas" "30.0.5"
    "@types/istanbul-lib-coverage" "^2.0.6"
    "@types/istanbul-reports" "^3.0.4"
    "@types/node" "*"
    "@types/yargs" "^17.0.33"
    chalk "^4.1.2"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.13"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz"
  integrity sha1-Y0Khn0Q0dRjJPkOxrGnes8Rlah8=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  integrity sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.23", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.30"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.30.tgz"
  integrity sha1-SnbE2u7l3wn105QOCHRC+zbOK5k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jsonjoy.com/base64@^1.1.1":
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jsonjoy.com/base64/-/base64-1.1.2.tgz"
  integrity sha1-z46p3LhJuByV8U/AqqFRxrVNJXg=

"@jsonjoy.com/json-pack@^1.0.3":
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jsonjoy.com/json-pack/-/json-pack-1.1.1.tgz"
  integrity sha1-Hy2xmrH9MwTMrCWaHvHcav9t8Lo=
  dependencies:
    "@jsonjoy.com/base64" "^1.1.1"
    "@jsonjoy.com/util" "^1.1.2"
    hyperdyperid "^1.2.0"
    thingies "^1.20.0"

"@jsonjoy.com/util@^1.1.2", "@jsonjoy.com/util@^1.3.0":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@jsonjoy.com/util/-/util-1.5.0.tgz"
  integrity sha1-YAjjW52djuJ7xL+qcMjL8zpTe0w=

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz"
  integrity sha1-T8VsFcWAua233DwzOhNOVAtEv7E=

"@napi-rs/wasm-runtime@^0.2.11":
  version "0.2.12"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz"
  integrity sha1-PniouW5sM6bFF+GJTvvVOFp8tvI=
  dependencies:
    "@emnapi/core" "^1.4.3"
    "@emnapi/runtime" "^1.4.3"
    "@tybys/wasm-util" "^0.10.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@pkgr/core@^0.2.9":
  version "0.2.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@pkgr/core/-/core-0.2.9.tgz"
  integrity sha1-0imnt/nawWehVpku8jx/AjZT9Ts=

"@progress/kendo-common@^1.0.1":
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-common/-/kendo-common-1.0.2.tgz"
  integrity sha1-AzgfAL2WIXtepmQc21tDNTdd+Ic=
  dependencies:
    tslib "^1.7.0"

"@progress/kendo-draggable-common@^0.2.3":
  version "0.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-draggable-common/-/kendo-draggable-common-0.2.3.tgz"
  integrity sha1-tGA9cyOahdE7Rn8RIE229U6DHG8=
  dependencies:
    tslib "^1.7.0"

"@progress/kendo-drawing@^1.21.2":
  version "1.21.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-drawing/-/kendo-drawing-1.21.2.tgz"
  integrity sha1-I9CEiNRXXaz0yvTKCb9D9AbK9H0=
  dependencies:
    "@progress/kendo-common" "^1.0.1"
    "@progress/pako-esm" "^1.0.1"

"@progress/kendo-inputs-common@^3.1.0":
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-inputs-common/-/kendo-inputs-common-3.1.2.tgz"
  integrity sha1-VwnFr9OW4+I3sw8Orr96spckiRw=
  dependencies:
    tslib "^2.3.1"

"@progress/kendo-intl@^3.1.1":
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-intl/-/kendo-intl-3.1.2.tgz"
  integrity sha1-/4ewUcpro24pDrDwctKewolS2iE=

"@progress/kendo-licensing@^1.6.0", "@progress/kendo-licensing@^1.7.0":
  version "1.7.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-licensing/-/kendo-licensing-1.7.1.tgz"
  integrity sha1-8htqbiOOJTrI4It182jR9VLfuGY=
  dependencies:
    glob "^10.4.5"
    jsonwebtoken "^9.0.2"

"@progress/kendo-popup-common@^1.9.0":
  version "1.9.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-popup-common/-/kendo-popup-common-1.9.2.tgz"
  integrity sha1-1bVtIryMbOgS3SlzrSsiq/rIcg8=

"@progress/kendo-react-animation@^11.2.0", "@progress/kendo-react-animation@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-animation/-/kendo-react-animation-11.4.0.tgz"
  integrity sha1-vaSR0qJ+X7u9gundz4sd0eP8viU=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-buttons@^11.2.0", "@progress/kendo-react-buttons@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-buttons/-/kendo-react-buttons-11.4.0.tgz"
  integrity sha1-G4/6HZqUydlNCRKixnoclOhx25s=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-common@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-common/-/kendo-react-common-11.4.0.tgz"
  integrity sha1-3RIIJdIKCJDJgDt7ynuaMRfbvh0=
  dependencies:
    "@progress/kendo-draggable-common" "^0.2.3"
    prop-types "^15.6.0"

"@progress/kendo-react-dialogs@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-dialogs/-/kendo-react-dialogs-11.4.0.tgz"
  integrity sha1-T7aLmU7x/IoAXwoysqApobUn4n8=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-dropdowns@^11.2.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-dropdowns/-/kendo-react-dropdowns-11.4.0.tgz"
  integrity sha1-oqZkZPuCT3GRg2bWueYJamr0f8Q=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-inputs@^11.2.0", "@progress/kendo-react-inputs@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-inputs/-/kendo-react-inputs-11.4.0.tgz"
  integrity sha1-qYjzVY4BUx3gmpFDSpPVnFhhkp8=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-intl@^11.2.0", "@progress/kendo-react-intl@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-intl/-/kendo-react-intl-11.4.0.tgz"
  integrity sha1-rv/9j5MsAO5aqH8vXKcUsbJUwE0=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-labels@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-labels/-/kendo-react-labels-11.4.0.tgz"
  integrity sha1-Kv0ACeCTOfOkxRbG6BHkkDGptvU=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-layout@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-layout/-/kendo-react-layout-11.4.0.tgz"
  integrity sha1-eqIp6+BVVrAgPk4LNOO4vX47+As=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-popup@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-popup/-/kendo-react-popup-11.4.0.tgz"
  integrity sha1-7QOhG0G9cS9w2e4OxaCnSAmbL48=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-progressbars@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-progressbars/-/kendo-react-progressbars-11.4.0.tgz"
  integrity sha1-v4ytiBNqOreidPGFOFYKYJ9NrC8=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-react-treeview@^11.2.0", "@progress/kendo-react-treeview@11.4.0":
  version "11.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-react-treeview/-/kendo-react-treeview-11.4.0.tgz"
  integrity sha1-Wc4c+EshGBLJiFWLqcBkAUDHjwc=
  dependencies:
    prop-types "^15.6.0"

"@progress/kendo-svg-icons@^4.0.0", "@progress/kendo-svg-icons@^4.1.0":
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-svg-icons/-/kendo-svg-icons-4.5.0.tgz"
  integrity sha1-NXi9zFV+i3TLsfKqrzFLl7RVPAc=

"@progress/kendo-theme-core@11.3.2":
  version "11.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-theme-core/-/kendo-theme-core-11.3.2.tgz"
  integrity sha1-6/CuY4Hx88i4VswkUqFpwdmKamc=

"@progress/kendo-theme-default@^11.0.2":
  version "11.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-theme-default/-/kendo-theme-default-11.3.2.tgz"
  integrity sha1-dYkmpmkRVf+5xVjfVraTBWeW2oA=
  dependencies:
    "@progress/kendo-svg-icons" "^4.1.0"
    "@progress/kendo-theme-core" "11.3.2"
    "@progress/kendo-theme-utils" "11.3.2"

"@progress/kendo-theme-utils@11.3.2":
  version "11.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-theme-utils/-/kendo-theme-utils-11.3.2.tgz"
  integrity sha1-vcPYg9NKqPOl9DW+s2MJOOrX9mo=
  dependencies:
    "@progress/kendo-theme-core" "11.3.2"

"@progress/kendo-webspeech-common@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/kendo-webspeech-common/-/kendo-webspeech-common-1.0.1.tgz"
  integrity sha1-KRE7IOUg6YxHKmryYgT2BuZXMUg=

"@progress/pako-esm@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@progress/pako-esm/-/pako-esm-1.0.1.tgz"
  integrity sha1-7rfSw/joZRmO3yP7QmRb2MtROhE=

"@rtsao/scc@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@rtsao/scc/-/scc-1.1.0.tgz"
  integrity sha1-kn3S+um8M2FAOsLHoAwy3c6a1+g=

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@sinclair/typebox/-/typebox-0.27.8.tgz"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@sinclair/typebox@^0.34.0":
  version "0.34.41"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@sinclair/typebox/-/typebox-0.34.41.tgz"
  integrity sha1-qlGmwZRt8sWhFJSizbkxjgJtsWw=

"@sinonjs/commons@^3.0.1":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@sinonjs/commons/-/commons-3.0.1.tgz"
  integrity sha1-ECk1fkTKkBphVYX20nc428iQhM0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^13.0.0":
  version "13.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz"
  integrity sha1-NrnbwhrVVGSG6pFz1r6gY+sXF9U=
  dependencies:
    "@sinonjs/commons" "^3.0.1"

"@standard-schema/utils@^0.3.0":
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@standard-schema/utils/-/utils-0.3.0.tgz"
  integrity sha1-PV5gjxbCOQwQUo6Y5Zrva/c8rns=

"@testing-library/dom@^10.0.0", "@testing-library/dom@^10.4.0", "@testing-library/dom@^8.0.0 || ^9.0.0 || ^10.0.0", "@testing-library/dom@>=7.21.4":
  version "10.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@testing-library/dom/-/dom-10.4.0.tgz"
  integrity sha1-gqnZRi8R0kDsrb9AZgfGzu7/Q6g=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    aria-query "5.3.0"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.5.0"
    pretty-format "^27.0.2"

"@testing-library/jest-dom@^6.6.3":
  version "6.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@testing-library/jest-dom/-/jest-dom-6.6.3.tgz"
  integrity sha1-JrqQbPkowPgXLhgsb+IU60+fK9I=
  dependencies:
    "@adobe/css-tools" "^4.4.0"
    aria-query "^5.0.0"
    chalk "^3.0.0"
    css.escape "^1.5.1"
    dom-accessibility-api "^0.6.3"
    lodash "^4.17.21"
    redent "^3.0.0"

"@testing-library/react@^16.3.0":
  version "16.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@testing-library/react/-/react-16.3.0.tgz"
  integrity sha1-OoW7m96/GAzXbboWRU4kJWTVmKY=
  dependencies:
    "@babel/runtime" "^7.12.5"

"@testing-library/user-event@^14.6.1":
  version "14.6.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@testing-library/user-event/-/user-event-14.6.1.tgz"
  integrity sha1-E+CaMteotwYP44MEeI6/QZfNIUk=

"@tsconfig/node10@^1.0.7":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@tsconfig/node10/-/node10-1.0.11.tgz"
  integrity sha1-buRkAGhfEw4ngSjHs4t+Ax/1svI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=

"@tybys/wasm-util@^0.10.0":
  version "0.10.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@tybys/wasm-util/-/wasm-util-0.10.0.tgz"
  integrity sha1-L9PNdUuUs3hzTOFwWNBQfEXIg2k=
  dependencies:
    tslib "^2.4.0"

"@types/aria-query@^5.0.1":
  version "5.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/aria-query/-/aria-query-5.0.4.tgz"
  integrity sha1-GjHD03iFDSd42rtjdNA23LpLpwg=

"@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  integrity sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.28.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/babel__traverse/-/babel__traverse-7.28.0.tgz"
  integrity sha1-B9cT1szg0mXJhJ2wy+YtP2Hzb3Q=
  dependencies:
    "@babel/types" "^7.28.2"

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/body-parser/-/body-parser-1.19.5.tgz"
  integrity sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.13":
  version "3.5.13"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/bonjour/-/bonjour-3.5.13.tgz"
  integrity sha1-rfkM4aEF6B3R+cYf3Fr9ob+5KVY=
  dependencies:
    "@types/node" "*"

"@types/cheerio@<1":
  version "0.22.35"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/cheerio/-/cheerio-0.22.35.tgz"
  integrity sha1-DRbcHyTUJiMcGBucMYR/ZzhnWV8=
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.5.4":
  version "1.5.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz"
  integrity sha1-fecWRaEDBWtIrDzgezUguBnB1bM=
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/connect/-/connect-3.4.38.tgz"
  integrity sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=
  dependencies:
    "@types/node" "*"

"@types/copy-webpack-plugin@^10.1.3":
  version "10.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/copy-webpack-plugin/-/copy-webpack-plugin-10.1.3.tgz"
  integrity sha1-Kepg5Z6dB795ijQ+mhAM2bTwNCE=
  dependencies:
    copy-webpack-plugin "*"

"@types/enzyme@3.10.19":
  version "3.10.19"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/enzyme/-/enzyme-3.10.19.tgz"
  integrity sha1-pK51HBPgQoFx1TzcDT2fmsp/dok=
  dependencies:
    "@types/cheerio" "<1"
    "@types/react" "^16"

"@types/eslint-scope@^3.7.7":
  version "3.7.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  integrity sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@^9.6.1", "@types/eslint@>=8.0.0":
  version "9.6.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/eslint/-/eslint-9.6.1.tgz"
  integrity sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6", "@types/estree@^1.0.8":
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/estree/-/estree-1.0.8.tgz"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/express-serve-static-core@*":
  version "5.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-5.0.3.tgz"
  integrity sha1-BBdNPwg2hjRnt/vLu81pRB0gVxU=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-serve-static-core@^4.17.21":
  version "4.19.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz"
  integrity sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.19.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz"
  integrity sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.13", "@types/express@^4.17.21":
  version "4.17.21"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/express/-/express-4.17.21.tgz"
  integrity sha1-wm1KFR5g7+AISyPcM2nrxjHtGS0=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/fork-ts-checker-webpack-plugin@^0.4.5":
  version "0.4.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-0.4.5.tgz"
  integrity sha1-HJ754JbqTk41VQUBmArE9y4e69c=
  dependencies:
    fork-ts-checker-webpack-plugin "*"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/glob/-/glob-7.2.0.tgz"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/history@^4.7.11":
  version "4.7.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/history/-/history-4.7.11.tgz"
  integrity sha1-VliLF66PUMU5g6Uk/DzEdDeWnWQ=

"@types/hoist-non-react-statics@*":
  version "3.3.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.6.tgz"
  integrity sha1-a7p0ODzauY6NtOIM5bSmuYyu0BA=
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  integrity sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/http-errors/-/http-errors-2.0.4.tgz"
  integrity sha1-frR3JsORtzRabsNa1/TeRpz1uk8=

"@types/http-proxy@^1.17.8":
  version "1.17.16"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/http-proxy/-/http-proxy-1.17.16.tgz"
  integrity sha1-3uNgcHs1s8yFr83on/7r/31/kkA=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1", "@types/istanbul-lib-coverage@^2.0.6":
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz"
  integrity sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz"
  integrity sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0", "@types/istanbul-reports@^3.0.4":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz"
  integrity sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^30.0.0":
  version "30.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/jest/-/jest-30.0.0.tgz"
  integrity sha1-XoWuVoAGcS5K1m8lQz6b2siAHx0=
  dependencies:
    expect "^30.0.0"
    pretty-format "^30.0.0"

"@types/jsdom@^21.1.7":
  version "21.1.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/jsdom/-/jsdom-21.1.7.tgz"
  integrity sha1-ntywngsHzodueDOSLTJ0FJyJjPo=
  dependencies:
    "@types/node" "*"
    "@types/tough-cookie" "*"
    parse5 "^7.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.15", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/json5/-/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/mime@^1":
  version "1.3.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/mime/-/mime-1.3.5.tgz"
  integrity sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=

"@types/minimatch@*":
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/minimatch/-/minimatch-5.1.2.tgz"
  integrity sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=

"@types/node-forge@^1.3.0":
  version "1.3.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/node-forge/-/node-forge-1.3.11.tgz"
  integrity sha1-CXLqU43bD02cL6DsXbVyR3OmBNo=
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@^24.0.10":
  version "24.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/node/-/node-24.3.0.tgz"
  integrity sha1-ibCfRcuajuaUZvGO5YZOTD64Tew=
  dependencies:
    undici-types "~7.10.0"

"@types/prop-types@*":
  version "15.7.14"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/prop-types/-/prop-types-15.7.14.tgz"
  integrity sha1-FDNBnXOyp+v8aRjc79LsDVzWmPI=

"@types/qs@*":
  version "6.9.17"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/qs/-/qs-6.9.17.tgz"
  integrity sha1-/FYPYJRtCu/y+RTrQWeWWdMxDho=

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=

"@types/react-dom@^18.0.0 || ^19.0.0", "@types/react-dom@^18.3.5":
  version "18.3.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/react-dom/-/react-dom-18.3.7.tgz"
  integrity sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=

"@types/react-router-dom@^5.3.3":
  version "5.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/react-router-dom/-/react-router-dom-5.3.3.tgz"
  integrity sha1-6da0pm/NvWUaXxBsJlajAIjMHoM=
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"
    "@types/react-router" "*"

"@types/react-router@*":
  version "5.1.20"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/react-router/-/react-router-5.1.20.tgz"
  integrity sha1-iOzKoSKoJAXvPvvKql3N2fAhOHw=
  dependencies:
    "@types/history" "^4.7.11"
    "@types/react" "*"

"@types/react-toastify@^4.1.0":
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/react-toastify/-/react-toastify-4.1.0.tgz"
  integrity sha1-YE5xKFXdZ3kW1cZq9ZXTtZD12V0=
  dependencies:
    react-toastify "*"

"@types/react@*", "@types/react@^18.0.0", "@types/react@^18.0.0 || ^19.0.0", "@types/react@^18.3.12":
  version "18.3.24"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/react/-/react-18.3.24.tgz"
  integrity sha1-9qWkxhMkLf468NzuK07Ee5LZtr0=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/react@^16":
  version "16.14.62"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/react/-/react-16.14.62.tgz"
  integrity sha1-RJ5OgcqvEy0MLDkGROV3cC2x3Z4=
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "^0.16"
    csstype "^3.0.2"

"@types/retry@0.12.2":
  version "0.12.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/retry/-/retry-0.12.2.tgz"
  integrity sha1-7SeaZPpDi7afJIDtpEk3kSu3SAo=

"@types/scheduler@^0.16":
  version "0.16.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/scheduler/-/scheduler-0.16.8.tgz"
  integrity sha1-zlrOBM/qvn74fACR5QdS42cH3v8=

"@types/send@*":
  version "0.17.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/send/-/send-0.17.4.tgz"
  integrity sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.4":
  version "1.9.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/serve-index/-/serve-index-1.9.4.tgz"
  integrity sha1-5q4T1QU8sG7TY5IRC0+aSaxOyJg=
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.15.5":
  version "1.15.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/serve-static/-/serve-static-1.15.7.tgz"
  integrity sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/sockjs@^0.3.36":
  version "0.3.36"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/sockjs/-/sockjs-0.3.36.tgz"
  integrity sha1-zjIs8HvMEZ1Mv3+IlU86O9D2dTU=
  dependencies:
    "@types/node" "*"

"@types/stack-utils@^2.0.3":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/stack-utils/-/stack-utils-2.0.3.tgz"
  integrity sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=

"@types/styled-components@^5.1.34":
  version "5.1.34"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/styled-components/-/styled-components-5.1.34.tgz"
  integrity sha1-QQffjvin6rpPprBfePk/uk2vAwA=
  dependencies:
    "@types/hoist-non-react-statics" "*"
    "@types/react" "*"
    csstype "^3.0.2"

"@types/stylis@4.2.5":
  version "4.2.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/stylis/-/stylis-4.2.5.tgz"
  integrity sha1-HapkVvQJWdBhV2mKZTqasKcCgd8=

"@types/tough-cookie@*":
  version "4.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/tough-cookie/-/tough-cookie-4.0.5.tgz"
  integrity sha1-y24qaRtwyxd8bjrpwdLosuqM0wQ=

"@types/webpack-dev-server@^4.7.2":
  version "4.7.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/webpack-dev-server/-/webpack-dev-server-4.7.2.tgz"
  integrity sha1-oS2YgaojzdTOy7LTH6eEpFxJZ+A=
  dependencies:
    webpack-dev-server "*"

"@types/webpack@^5.28.5":
  version "5.28.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/webpack/-/webpack-5.28.5.tgz"
  integrity sha1-Dp2aFe+gm72izvQTVspKwgMeqaI=
  dependencies:
    "@types/node" "*"
    tapable "^2.2.0"
    webpack "^5"

"@types/ws@^8.5.10":
  version "8.5.13"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/ws/-/ws-8.5.13.tgz"
  integrity sha1-ZBTCgIdeJpHQ0eCAsFrdv1y5HiA=
  dependencies:
    "@types/node" "*"

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/yargs-parser/-/yargs-parser-21.0.3.tgz"
  integrity sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=

"@types/yargs@^17.0.33", "@types/yargs@^17.0.8":
  version "17.0.33"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/yargs/-/yargs-17.0.33.tgz"
  integrity sha1-jDIwPag+7AUKhLPHrnufki0T4y0=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yup@^0.32.0":
  version "0.32.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@types/yup/-/yup-0.32.0.tgz"
  integrity sha1-KhnDu7s67RvXVSBPg+gA+clfJJo=
  dependencies:
    yup "*"

"@typescript-eslint/eslint-plugin@^8.35.1":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.42.0.tgz"
  integrity sha1-IXLQSWxC7ujHKUtmYWgRAJU/qI8=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.42.0"
    "@typescript-eslint/type-utils" "8.42.0"
    "@typescript-eslint/utils" "8.42.0"
    "@typescript-eslint/visitor-keys" "8.42.0"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@^8.35.1", "@typescript-eslint/parser@^8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/parser/-/parser-8.42.0.tgz"
  integrity sha1-IOpm9IZ5gftbtiy+FFQlD8SkQKs=
  dependencies:
    "@typescript-eslint/scope-manager" "8.42.0"
    "@typescript-eslint/types" "8.42.0"
    "@typescript-eslint/typescript-estree" "8.42.0"
    "@typescript-eslint/visitor-keys" "8.42.0"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/project-service/-/project-service-8.42.0.tgz"
  integrity sha1-Y26zQYtsQsmFVNzohJQ3CL9BpYM=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.42.0"
    "@typescript-eslint/types" "^8.42.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@^8.15.0", "@typescript-eslint/scope-manager@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-8.42.0.tgz"
  integrity sha1-NgFnV7yFtG6kK65Hth+UIe3e3eM=
  dependencies:
    "@typescript-eslint/types" "8.42.0"
    "@typescript-eslint/visitor-keys" "8.42.0"

"@typescript-eslint/tsconfig-utils@^8.42.0", "@typescript-eslint/tsconfig-utils@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.42.0.tgz"
  integrity sha1-IaPnQ5b9dEP/kwvEGyd4m6fpI24=

"@typescript-eslint/type-utils@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/type-utils/-/type-utils-8.42.0.tgz"
  integrity sha1-1nM+ep+99a9gwJxgON/94T9OQlM=
  dependencies:
    "@typescript-eslint/types" "8.42.0"
    "@typescript-eslint/typescript-estree" "8.42.0"
    "@typescript-eslint/utils" "8.42.0"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@^8.42.0", "@typescript-eslint/types@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/types/-/types-8.42.0.tgz"
  integrity sha1-rhXAnOvaIEc3cpAgMzKOhzctsAg=

"@typescript-eslint/typescript-estree@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-8.42.0.tgz"
  integrity sha1-WTw6+H1EYiUsDXI50XILhKG1aGQ=
  dependencies:
    "@typescript-eslint/project-service" "8.42.0"
    "@typescript-eslint/tsconfig-utils" "8.42.0"
    "@typescript-eslint/types" "8.42.0"
    "@typescript-eslint/visitor-keys" "8.42.0"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@^8.15.0", "@typescript-eslint/utils@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/utils/-/utils-8.42.0.tgz"
  integrity sha1-lfjgxpf/L32l9y4WE1AR+HjYFcA=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.42.0"
    "@typescript-eslint/types" "8.42.0"
    "@typescript-eslint/typescript-estree" "8.42.0"

"@typescript-eslint/visitor-keys@8.42.0":
  version "8.42.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-8.42.0.tgz"
  integrity sha1-h8bKqhrDB7xzqHwfxGn4jwFi8n4=
  dependencies:
    "@typescript-eslint/types" "8.42.0"
    eslint-visitor-keys "^4.2.1"

"@ungap/structured-clone@^1.3.0":
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@ungap/structured-clone/-/structured-clone-1.3.0.tgz"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

"@unrs/resolver-binding-android-arm-eabi@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-android-arm-eabi/-/resolver-binding-android-arm-eabi-1.11.1.tgz"
  integrity sha1-n1sEUDCI5qNUKV6OqP48uZ5Dr4E=

"@unrs/resolver-binding-android-arm64@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-android-arm64/-/resolver-binding-android-arm64-1.11.1.tgz"
  integrity sha1-dBSIVDG9cXi5ia7cTSXMyzhlvJ8=

"@unrs/resolver-binding-darwin-arm64@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-darwin-arm64/-/resolver-binding-darwin-arm64-1.11.1.tgz"
  integrity sha1-tKhVb0IXH7nJ97rII1BF6Cqgy98=

"@unrs/resolver-binding-darwin-x64@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-darwin-x64/-/resolver-binding-darwin-x64-1.11.1.tgz"
  integrity sha1-/U2BJXsT9NGgg4kKahfADeVx8Nw=

"@unrs/resolver-binding-freebsd-x64@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-freebsd-x64/-/resolver-binding-freebsd-x64-1.11.1.tgz"
  integrity sha1-0lEwhNDzfEB3V+IvMr2SSnjP2Zs=

"@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-arm-gnueabihf/-/resolver-binding-linux-arm-gnueabihf-1.11.1.tgz"
  integrity sha1-hE0mBdBXSI13+rCXBfKGa4YWTgo=

"@unrs/resolver-binding-linux-arm-musleabihf@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-arm-musleabihf/-/resolver-binding-linux-arm-musleabihf-1.11.1.tgz"
  integrity sha1-IEiSmVzvtr0dAX1S0JcZO8Yd2tM=

"@unrs/resolver-binding-linux-arm64-gnu@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-arm64-gnu/-/resolver-binding-linux-arm64-gnu-1.11.1.tgz"
  integrity sha1-Aj6ww6rEYGahC+ej82Lns087350=

"@unrs/resolver-binding-linux-arm64-musl@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-arm64-musl/-/resolver-binding-linux-arm64-musl-1.11.1.tgz"
  integrity sha1-nm+auwZCTjFApgrJlhOXhvXZm+A=

"@unrs/resolver-binding-linux-ppc64-gnu@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-ppc64-gnu/-/resolver-binding-linux-ppc64-gnu-1.11.1.tgz"
  integrity sha1-sRFBfxfJ0bAu++yOCDmPDFUnu0Q=

"@unrs/resolver-binding-linux-riscv64-gnu@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-riscv64-gnu/-/resolver-binding-linux-riscv64-gnu-1.11.1.tgz"
  integrity sha1-kv+/AnSK8+mYc5RcmoperQHVCKk=

"@unrs/resolver-binding-linux-riscv64-musl@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-riscv64-musl/-/resolver-binding-linux-riscv64-musl-1.11.1.tgz"
  integrity sha1-C+xvElj8OQ5rMF6f9EJWyyB94WU=

"@unrs/resolver-binding-linux-s390x-gnu@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-s390x-gnu/-/resolver-binding-linux-s390x-gnu-1.11.1.tgz"
  integrity sha1-V3hDoITFlS9ZBncGM8z7idrJvJQ=

"@unrs/resolver-binding-linux-x64-gnu@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-x64-gnu/-/resolver-binding-linux-x64-gnu-1.11.1.tgz"
  integrity sha1-NvsxjuvdaQ9toyrF4EmadvqIGTU=

"@unrs/resolver-binding-linux-x64-musl@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-linux-x64-musl/-/resolver-binding-linux-x64-musl-1.11.1.tgz"
  integrity sha1-v7mvdfeD+Y9qIsQkQhTv5N8YU9Y=

"@unrs/resolver-binding-wasm32-wasi@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-wasm32-wasi/-/resolver-binding-wasm32-wasi-1.11.1.tgz"
  integrity sha1-dSw1ndh1aEsnQpUA2IIm18xy9x0=
  dependencies:
    "@napi-rs/wasm-runtime" "^0.2.11"

"@unrs/resolver-binding-win32-arm64-msvc@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-win32-arm64-msvc/-/resolver-binding-win32-arm64-msvc-1.11.1.tgz"
  integrity sha1-zlc15gDkwvu0Cc0FGzt9pKOZrzU=

"@unrs/resolver-binding-win32-ia32-msvc@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-win32-ia32-msvc/-/resolver-binding-win32-ia32-msvc-1.11.1.tgz"
  integrity sha1-cvxXvHxk7Fw94NZO4NGBAxe8YKY=

"@unrs/resolver-binding-win32-x64-msvc@1.11.1":
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@unrs/resolver-binding-win32-x64-msvc/-/resolver-binding-win32-x64-msvc-1.11.1.tgz"
  integrity sha1-U4seEDv42YZOe4XMlvqNb7bEB3c=

"@webassemblyjs/ast@^1.14.1", "@webassemblyjs/ast@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  integrity sha1-qfagfysDyVyNOMRTah/ftSH/VbY=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  integrity sha1-/Moe7dscxOe27tT8eVbWgTshufs=

"@webassemblyjs/helper-api-error@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  integrity sha1-4KFhUiSLw42u523X4h8Vxe86sec=

"@webassemblyjs/helper-buffer@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  integrity sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=

"@webassemblyjs/helper-numbers@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  integrity sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  integrity sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=

"@webassemblyjs/helper-wasm-section@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  integrity sha1-lindqcRDDqtUtZEFPW3G87oFA0g=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  integrity sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  integrity sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  version "1.13.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  integrity sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=

"@webassemblyjs/wasm-edit@^1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  integrity sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  integrity sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  integrity sha1-5vce18yuRngcIGAX08FMUO+oEGs=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@^1.14.1", "@webassemblyjs/wasm-parser@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  integrity sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  integrity sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^3.0.1":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webpack-cli/configtest/-/configtest-3.0.1.tgz"
  integrity sha1-dqwoW5ZY+mQs4jjCdiZFiaora1c=

"@webpack-cli/info@^3.0.1":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webpack-cli/info/-/info-3.0.1.tgz"
  integrity sha1-PP83+rt9TsqraopHV9OCbPWIjGM=

"@webpack-cli/serve@^3.0.1":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@webpack-cli/serve/-/serve-3.0.1.tgz"
  integrity sha1-vYsfgk1X4w+qGet45MCVEFb3LwA=

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

accepts@~1.3.4, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/accepts/-/accepts-1.3.8.tgz"
  integrity sha1-C/C+EltnAUrcsLCSHmLbe//hay4=
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-phases@^1.0.3:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/acorn-import-phases/-/acorn-import-phases-1.0.4.tgz"
  integrity sha1-FuuFC6maBWy3y/6HL/uJcuGMi9c=

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.1.1:
  version "8.3.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/acorn-walk/-/acorn-walk-8.3.4.tgz"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.11.0, acorn@^8.14.0, acorn@^8.15.0, acorn@^8.4.1, acorn@^8.8.2:
  version "8.15.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/acorn/-/acorn-8.15.0.tgz"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/agent-base/-/agent-base-7.1.4.tgz"
  integrity sha1-48121MVI7oldPD/Y3B9sW5Ay56g=

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  integrity sha1-adTThaRzPNvqtElkoRcKiPh/DhY=
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.12.4, ajv@^6.12.5, ajv@^6.9.1:
  version "6.12.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ajv/-/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0:
  version "8.17.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ajv/-/ajv-8.17.1.tgz"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ajv@^8.8.2, ajv@^8.9.0:
  version "8.17.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ajv/-/ajv-8.17.1.tgz"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ansi-escapes@^4.3.2:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-regex/-/ansi-regex-6.2.0.tgz"
  integrity sha1-LzAudVBDGxt3YnBf/7Us8f+iBEc=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

anymatch@^3.1.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/arg/-/arg-4.1.3.tgz"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/argparse/-/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/argparse/-/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@^5.0.0, aria-query@5.3.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/aria-query/-/aria-query-5.3.0.tgz"
  integrity sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz"
  integrity sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-includes@^3.1.6, array-includes@^3.1.8, array-includes@^3.1.9:
  version "3.1.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array-includes/-/array-includes-3.1.9.tgz"
  integrity sha1-HwzKoI6Qzbw+tDMhD5A60PF8Pzo=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.24.0"
    es-object-atoms "^1.1.1"
    get-intrinsic "^1.3.0"
    is-string "^1.1.1"
    math-intrinsics "^1.1.0"

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array-union/-/array-union-1.0.2.tgz"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array-uniq/-/array-uniq-1.0.3.tgz"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz"
  integrity sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.findlastindex@^1.2.6:
  version "1.2.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.6.tgz"
  integrity sha1-z6EGXIHctk40VXybgdAS9qQhxWQ=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-shim-unscopables "^1.1.0"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz"
  integrity sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz"
  integrity sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz"
  integrity sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz"
  integrity sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@^1.11.0, axios@^1.8.2, axios@^1.8.4:
  version "1.11.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/axios/-/axios-1.11.0.tgz"
  integrity sha1-wuwhnjXkFMAlsglei4KAJ4R4/bY=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.4"
    proxy-from-env "^1.1.0"

axon-core-ui-shared@^1.1.146-dev:
  version "1.1.146-dev"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/axon-core-ui-shared/-/axon-core-ui-shared-1.1.146-dev.tgz"
  integrity sha1-cT6s/hOXES20LjW6zHwyiVUWt/4=

"babel-jest@^29.0.0 || ^30.0.0", babel-jest@^30.0.4, babel-jest@30.1.2:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-jest/-/babel-jest-30.1.2.tgz"
  integrity sha1-3s1Ts6DK/KSUQ/k/t6LA+6VVENo=
  dependencies:
    "@jest/transform" "30.1.2"
    "@types/babel__core" "^7.20.5"
    babel-plugin-istanbul "^7.0.0"
    babel-preset-jest "30.0.1"
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    slash "^3.0.0"

babel-loader@^10.0.0:
  version "10.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-loader/-/babel-loader-10.0.0.tgz"
  integrity sha1-uXQ3FMDh4ISz5K3vPNX67jMImXc=
  dependencies:
    find-up "^5.0.0"

babel-plugin-istanbul@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-plugin-istanbul/-/babel-plugin-istanbul-7.0.0.tgz"
  integrity sha1-YpoXj2O4Pcns7kb9ICZig7HxEoA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-instrument "^6.0.2"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@30.0.1:
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.1.tgz"
  integrity sha1-8nGyBm0sH7JqhjrbjhP4WwYkcSU=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    "@types/babel__core" "^7.20.5"

babel-plugin-polyfill-corejs2@^0.4.14:
  version "0.4.14"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.14.tgz"
  integrity sha1-gQG4K3acVog1YRVCSI1GM5XC748=
  dependencies:
    "@babel/compat-data" "^7.27.7"
    "@babel/helper-define-polyfill-provider" "^0.6.5"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.13.0:
  version "0.13.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.13.0.tgz"
  integrity sha1-u39q7vet3/F/dgKgim0ZoSjDAWQ=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.5"
    core-js-compat "^3.43.0"

babel-plugin-polyfill-regenerator@^0.6.5:
  version "0.6.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.5.tgz"
  integrity sha1-MnUuOKtvZ2e5JlA0e/JqMbFq6MU=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.5"

babel-preset-current-node-syntax@^1.1.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.2.0.tgz"
  integrity sha1-IHMNbNx92l2JQByrEKxqMgZ6zeY=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-jest@30.0.1:
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/babel-preset-jest/-/babel-preset-jest-30.0.1.tgz"
  integrity sha1-fSjblTG84mToRshIPVQjYkS4rog=
  dependencies:
    babel-plugin-jest-hoist "30.0.1"
    babel-preset-current-node-syntax "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

batch@0.6.1:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/batch/-/batch-0.6.1.tgz"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

body-parser@1.20.3:
  version "1.20.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/body-parser/-/body-parser-1.20.3.tgz"
  integrity sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.2.1:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/bonjour-service/-/bonjour-service-1.3.0.tgz"
  integrity sha1-gNhnQwtaDaZOgqgEf8HjVb23FyI=
  dependencies:
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/braces/-/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, browserslist@^4.25.3, "browserslist@>= 4.21.0":
  version "4.25.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/browserslist/-/browserslist-4.25.4.tgz"
  integrity sha1-690OHRzzkRg0urOmzXuRfZur9a8=
  dependencies:
    caniuse-lite "^1.0.30001737"
    electron-to-chromium "^1.5.211"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bs-logger@^0.2.6:
  version "0.2.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/bs-logger/-/bs-logger-0.2.6.tgz"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/bser/-/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

bundle-name@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/bundle-name/-/bundle-name-4.1.0.tgz"
  integrity sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=
  dependencies:
    run-applescript "^7.0.0"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/bytes/-/bytes-3.1.2.tgz"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/call-bind/-/call-bind-1.0.8.tgz"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/call-bound/-/call-bound-1.0.4.tgz"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0, callsites@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/callsites/-/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/camel-case/-/camel-case-4.1.2.tgz"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.3.0:
  version "6.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

camelize@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/camelize/-/camelize-1.0.1.tgz"
  integrity sha1-ibfhaIQFYzGjXWta0GQzLJHapsM=

caniuse-lite@^1.0.30001737:
  version "1.0.30001739"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001739.tgz"
  integrity sha1-s0zi1Wv8IvQ1KyrwFEEC1iOhJPQ=

cenforge-api-sdk@1.0.11-feature:
  version "1.0.11-feature"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cenforge-api-sdk/-/cenforge-api-sdk-1.0.11-feature.tgz"
  integrity sha1-lKpcsl8Fp7yG6ID+28pAbBzqkA8=
  dependencies:
    axios "^1.11.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/chalk/-/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/chalk/-/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/char-regex/-/char-regex-1.0.2.tgz"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.1:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/chokidar/-/chokidar-4.0.3.tgz"
  integrity sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=
  dependencies:
    readdirp "^4.0.1"

chrome-trace-event@^1.0.2:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  integrity sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ci-info/-/ci-info-3.9.0.tgz"
  integrity sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=

ci-info@^4.2.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ci-info/-/ci-info-4.3.0.tgz"
  integrity sha1-w5sQE/j9vSjNeOYjGDV9AtoWDNc=

cjs-module-lexer@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cjs-module-lexer/-/cjs-module-lexer-2.1.0.tgz"
  integrity sha1-WG6H1DQcsmYYUOzlGQIyzN68/4s=

clean-css@^5.2.2:
  version "5.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/clean-css/-/clean-css-5.3.3.tgz"
  integrity sha1-szBlPNO9a3UAnMJccUyue5M1HM0=
  dependencies:
    source-map "~0.6.0"

clean-webpack-plugin@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/clean-webpack-plugin/-/clean-webpack-plugin-4.0.0.tgz"
  integrity sha1-cpR9RAPUUvOO1hqf8K2oEiqs1yk=
  dependencies:
    del "^4.1.1"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cliui/-/cliui-8.0.1.tgz"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/clone-deep/-/clone-deep-4.0.1.tgz"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clsx@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/clsx/-/clsx-2.1.1.tgz"
  integrity sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=

co@^4.6.0:
  version "4.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/co/-/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

collect-v8-coverage@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz"
  integrity sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/color-name/-/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.10, colorette@^2.0.14:
  version "2.0.20"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/colorette/-/colorette-2.0.20.tgz"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^12.1.0:
  version "12.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/commander/-/commander-12.1.0.tgz"
  integrity sha1-AUI7NvUBJZ/arE0OTWDJbJkVhdM=

commander@^2.20.0:
  version "2.20.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/commander/-/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^8.3.0:
  version "8.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/commander/-/commander-8.3.0.tgz"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

compressible@~2.0.18:
  version "2.0.18"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/compressible/-/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/compression/-/compression-1.7.5.tgz"
  integrity sha1-/dJWwKZC454xTEePbCzWVO3XTJM=
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.0.2"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz"
  integrity sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha1-i4K076yCUSoCuwsdzsnSxejrW/4=
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/content-type/-/content-type-1.0.5.tgz"
  integrity sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cookie/-/cookie-1.0.2.tgz"
  integrity sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=

cookie@0.7.1:
  version "0.7.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cookie/-/cookie-0.7.1.tgz"
  integrity sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=

copy-webpack-plugin@*, copy-webpack-plugin@^13.0.0:
  version "13.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/copy-webpack-plugin/-/copy-webpack-plugin-13.0.1.tgz"
  integrity sha1-+6GMIryrNjNSThtlJYD/RInt3A0=
  dependencies:
    glob-parent "^6.0.1"
    normalize-path "^3.0.0"
    schema-utils "^4.2.0"
    serialize-javascript "^6.0.2"
    tinyglobby "^0.2.12"

core-js-compat@^3.43.0:
  version "3.45.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/core-js-compat/-/core-js-compat-3.45.1.tgz"
  integrity sha1-Qk8/SvML9nb9G2eleUZRBPZOnHo=
  dependencies:
    browserslist "^4.25.3"

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^8.2.0:
  version "8.3.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/create-require/-/create-require-1.1.1.tgz"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-color-keywords@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/css-color-keywords/-/css-color-keywords-1.0.0.tgz"
  integrity sha1-/qJhbcZ2spYmhrOvjb2+GAskTgU=

css-loader@^7.1.2:
  version "7.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/css-loader/-/css-loader-7.1.2.tgz"
  integrity sha1-ZGcVQcbv4GsOIudQUDEGvdhogPg=
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.33"
    postcss-modules-extract-imports "^3.1.0"
    postcss-modules-local-by-default "^4.0.5"
    postcss-modules-scope "^3.2.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.5.4"

css-select@^4.1.3:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/css-select/-/css-select-4.3.0.tgz"
  integrity sha1-23EpsoRmYv2GKM/ElquytZ5BUps=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-to-react-native@3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/css-to-react-native/-/css-to-react-native-3.2.0.tgz"
  integrity sha1-zdgJn3ECThSeT2/hen1G7NVfHjI=
  dependencies:
    camelize "^1.0.0"
    css-color-keywords "^1.0.0"
    postcss-value-parser "^4.0.2"

css-what@^6.0.1:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/css-what/-/css-what-6.1.0.tgz"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

css.escape@^1.5.1:
  version "1.5.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/css.escape/-/css.escape-1.5.1.tgz"
  integrity sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssstyle@^4.2.1:
  version "4.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/cssstyle/-/cssstyle-4.6.0.tgz"
  integrity sha1-6hgAcCTjFn9PEFMV8+wtmCv0jtk=
  dependencies:
    "@asamuzakjp/css-color" "^3.2.0"
    rrweb-cssom "^0.8.0"

csstype@^3.0.2, csstype@3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/csstype/-/csstype-3.1.3.tgz"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

data-urls@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/data-urls/-/data-urls-5.0.0.tgz"
  integrity sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=
  dependencies:
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/data-view-buffer/-/data-view-buffer-1.0.2.tgz"
  integrity sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz"
  integrity sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz"
  integrity sha1-BoMH+bcat2274QKROJ4CCFZgYZE=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/debug/-/debug-3.2.7.tgz"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.4.1, debug@4:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/debug/-/debug-4.4.1.tgz"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

debug@2.6.9:
  version "2.6.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/debug/-/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

decimal.js@^10.5.0:
  version "10.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/decimal.js/-/decimal.js-10.6.0.tgz"
  integrity sha1-5kmkPjq5U6chkv9Zg4ZeUJ837Zo=

dedent@^1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dedent/-/dedent-1.6.0.tgz"
  integrity sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2, deepmerge@^4.3.1:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

default-browser-id@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/default-browser-id/-/default-browser-id-5.0.0.tgz"
  integrity sha1-odmL+WDBUILYo/pp6DFQzMzDryY=

default-browser@^5.2.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/default-browser/-/default-browser-5.2.1.tgz"
  integrity sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=
  dependencies:
    bundle-name "^4.1.0"
    default-browser-id "^5.0.0"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz"
  integrity sha1-27Ga37dG1/xtc0oGty9KANAhJV8=

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

del@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/del/-/del-4.1.1.tgz"
  integrity sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/depd/-/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

depd@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/depd/-/depd-2.0.0.tgz"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

dequal@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dequal/-/dequal-2.0.3.tgz"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

destroy@1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/destroy/-/destroy-1.2.0.tgz"
  integrity sha1-SANzVQmti+VSk0xn32FPlOZvoBU=

detect-newline@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/detect-newline/-/detect-newline-3.1.0.tgz"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/detect-node/-/detect-node-2.1.0.tgz"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diff@^4.0.1:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/diff/-/diff-4.0.2.tgz"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dns-packet@^5.2.2:
  version "5.6.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dns-packet/-/dns-packet-5.6.1.tgz"
  integrity sha1-roiK1CWp0UeKBnQlarhm3hASzy8=
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.16"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz"
  integrity sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=

dom-accessibility-api@^0.6.3:
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dom-accessibility-api/-/dom-accessibility-api-0.6.3.tgz"
  integrity sha1-mT6SXMHXPyxmLn113VpURSWaj9g=

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dom-converter/-/dom-converter-0.2.0.tgz"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dom-serializer/-/dom-serializer-1.4.1.tgz"
  integrity sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/domelementtype/-/domelementtype-2.3.0.tgz"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/domhandler/-/domhandler-4.3.1.tgz"
  integrity sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=
  dependencies:
    domelementtype "^2.2.0"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/domutils/-/domutils-2.8.0.tgz"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dot-case/-/dot-case-3.0.4.tgz"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv@^17.0.1:
  version "17.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dotenv/-/dotenv-17.2.2.tgz"
  integrity sha1-QBDP4cK+T8D0b9PZUa+0JLwGesY=

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha1-rg8PothQRe8UqBfao86azQSJ5b8=
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.5.211:
  version "1.5.213"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.213.tgz"
  integrity sha1-9DQYfyJ/t+Z7/PgkO5Wc884UAT4=

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/emittery/-/emittery-0.13.1.tgz"
  integrity sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

encodeurl@~2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/encodeurl/-/encodeurl-2.0.0.tgz"
  integrity sha1-e46omAd9fkCdOsRUdOo46vCFelg=

enhanced-resolve@^5.17.3, enhanced-resolve@^5.7.0:
  version "5.18.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/enhanced-resolve/-/enhanced-resolve-5.18.0.tgz"
  integrity sha1-kesdsZOJa5gBJR7v8caYAnix5AQ=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/entities/-/entities-2.2.0.tgz"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

entities@^6.0.0:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/entities/-/entities-6.0.1.tgz"
  integrity sha1-wow0pDN5yn9h0HQTCy9fcCCjBpQ=

envinfo@^7.14.0:
  version "7.14.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/envinfo/-/envinfo-7.14.0.tgz"
  integrity sha1-JtrF21RBjypMEVkVOgsq6YCDiq4=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9, es-abstract@^1.24.0:
  version "1.24.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-abstract/-/es-abstract-1.24.0.tgz"
  integrity sha1-xEcy0r6wrMHtYN+ECGnjEG568yg=
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.3.0"
    get-proto "^1.0.1"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-negative-zero "^2.0.3"
    is-regex "^1.2.1"
    is-set "^2.0.3"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.1"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.4"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.4"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    stop-iteration-iterator "^1.1.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.19"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz"
  integrity sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-module-lexer@^1.2.1:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-module-lexer/-/es-module-lexer-1.6.0.tgz"
  integrity sha1-2kn1h/2eaO4kBP5OJWwMfTqBviE=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2, es-shim-unscopables@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz"
  integrity sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/es-to-primitive/-/es-to-primitive-1.3.0.tgz"
  integrity sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/escalade/-/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-prettier@^10.1.5, "eslint-config-prettier@>= 7.0.0 <10.0.0 || >=10.1.0":
  version "10.1.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-config-prettier/-/eslint-config-prettier-10.1.8.tgz"
  integrity sha1-FXNM5K+MJ3jMMvCwGzewtc0ey5c=

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  integrity sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.12.1:
  version "2.12.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-module-utils/-/eslint-module-utils-2.12.1.tgz"
  integrity sha1-920yIL+4PAV2UTWSlatYVOqtdf8=
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@^2.32.0:
  version "2.32.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-plugin-import/-/eslint-plugin-import-2.32.0.tgz"
  integrity sha1-YCtV+qbkyuql6XDBmLXACjdwiYA=
  dependencies:
    "@rtsao/scc" "^1.1.0"
    array-includes "^3.1.9"
    array.prototype.findlastindex "^1.2.6"
    array.prototype.flat "^1.3.3"
    array.prototype.flatmap "^1.3.3"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.12.1"
    hasown "^2.0.2"
    is-core-module "^2.16.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.8"
    object.groupby "^1.0.3"
    object.values "^1.2.1"
    semver "^6.3.1"
    string.prototype.trimend "^1.0.9"
    tsconfig-paths "^3.15.0"

eslint-plugin-jest-dom@^5.5.0:
  version "5.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-plugin-jest-dom/-/eslint-plugin-jest-dom-5.5.0.tgz"
  integrity sha1-PM3+GX7dtBCPOQ21gwV6XazM1KA=
  dependencies:
    "@babel/runtime" "^7.16.3"
    requireindex "^1.2.0"

eslint-plugin-prettier@^5.5.1:
  version "5.5.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.4.tgz"
  integrity sha1-nWHE6hHeWvcE1O3xCMgsz6fy5hw=
  dependencies:
    prettier-linter-helpers "^1.0.0"
    synckit "^0.11.7"

eslint-plugin-react-hooks@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz"
  integrity sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=

eslint-plugin-react@^7.37.5:
  version "7.37.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-plugin-react/-/eslint-plugin-react-7.37.5.tgz"
  integrity sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-plugin-testing-library@^7.5.3:
  version "7.6.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-plugin-testing-library/-/eslint-plugin-testing-library-7.6.8.tgz"
  integrity sha1-Z5dXKV11P7SpZknRzSFHql3aXeY=
  dependencies:
    "@typescript-eslint/scope-manager" "^8.15.0"
    "@typescript-eslint/utils" "^8.15.0"

eslint-scope@^8.4.0:
  version "8.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-scope/-/eslint-scope-8.4.0.tgz"
  integrity sha1-iOZGogf61hQ2/6OetQUUcgBlXII=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.1:
  version "4.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz"
  integrity sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=

eslint-webpack-plugin@^5.0.2:
  version "5.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint-webpack-plugin/-/eslint-webpack-plugin-5.0.2.tgz"
  integrity sha1-4QjytEli3oaT9GDSgELHjfn5odM=
  dependencies:
    "@types/eslint" "^9.6.1"
    flatted "^3.3.3"
    jest-worker "^29.7.0"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    schema-utils "^4.3.0"

"eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^6.8.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "eslint@^8.0.0 || ^9.0.0", "eslint@^8.40 || 9", "eslint@^8.57.0 || ^9.0.0", eslint@^9.30.1, eslint@>=7.0.0, eslint@>=8.0.0:
  version "9.34.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eslint/-/eslint-9.34.0.tgz"
  integrity sha1-DqHywbXRZx248BqmuM5yIwIBb3s=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.21.0"
    "@eslint/config-helpers" "^0.3.1"
    "@eslint/core" "^0.15.2"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.34.0"
    "@eslint/plugin-kit" "^0.3.5"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.4.0"
    eslint-visitor-keys "^4.2.1"
    espree "^10.4.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"

espree@^10.0.1, espree@^10.4.0:
  version "10.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/espree/-/espree-10.4.0.tgz"
  integrity sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=
  dependencies:
    acorn "^8.15.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/esprima/-/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.5.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/esquery/-/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/esutils/-/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/etag/-/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.2.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/events/-/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/execa/-/execa-5.1.1.tgz"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit-x@^0.2.2:
  version "0.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/exit-x/-/exit-x-0.2.2.tgz"
  integrity sha1-H5BS3juNmaaWsQ2tW87ZvdXDqmQ=

expect@^30.0.0, expect@30.1.2:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/expect/-/expect-30.1.2.tgz"
  integrity sha1-CUkJwkQ/drniCPr6xKMVqq+SRYA=
  dependencies:
    "@jest/expect-utils" "30.1.2"
    "@jest/get-type" "30.1.0"
    jest-matcher-utils "30.1.2"
    jest-message-util "30.1.0"
    jest-mock "30.0.5"
    jest-util "30.0.5"

express@^4.21.2:
  version "4.21.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/express/-/express-4.21.2.tgz"
  integrity sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.3"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.7.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.3.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.3"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.12"
    proxy-addr "~2.0.7"
    qs "6.13.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.19.0"
    serve-static "1.16.2"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0, fast-json-stable-stringify@2.x:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-uri@^3.0.1:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fast-uri/-/fast-uri-3.0.3.tgz"
  integrity sha1-iSockYAtXXhg3nKPGGCKBXMUIkE=

fastest-levenshtein@^1.0.12:
  version "1.0.16"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fastq/-/fastq-1.19.1.tgz"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/faye-websocket/-/faye-websocket-0.11.4.tgz"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

fb-watchman@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fdir@^6.4.4:
  version "6.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fdir/-/fdir-6.5.0.tgz"
  integrity sha1-7Sq5Z6MxreYvGNB32uGSaE1Q01A=

file-entry-cache@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/file-entry-cache/-/file-entry-cache-8.0.0.tgz"
  integrity sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=
  dependencies:
    flat-cache "^4.0.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.3.1:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/finalhandler/-/finalhandler-1.3.1.tgz"
  integrity sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=
  dependencies:
    debug "2.6.9"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/find-up/-/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/find-up/-/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/flat-cache/-/flat-cache-4.0.1.tgz"
  integrity sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/flat/-/flat-5.0.2.tgz"
  integrity sha1-jKb+MyBp/6nTJMMnGYxZglnOskE=

flatted@^3.2.9, flatted@^3.3.3:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/flatted/-/flatted-3.3.3.tgz"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

follow-redirects@^1.0.0, follow-redirects@^1.15.6:
  version "1.15.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/follow-redirects/-/follow-redirects-1.15.11.tgz"
  integrity sha1-d31z1yqS+OxNLkEOtHNSpWuOg0A=

for-each@^0.3.3, for-each@^0.3.5:
  version "0.3.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/for-each/-/for-each-0.3.5.tgz"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/foreground-child/-/foreground-child-3.3.1.tgz"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

fork-ts-checker-webpack-plugin@*, fork-ts-checker-webpack-plugin@^9.1.0:
  version "9.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-9.1.0.tgz"
  integrity sha1-QzSBwcIoxWrxERcvytffeTGMkVo=
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    chokidar "^4.0.1"
    cosmiconfig "^8.2.0"
    deepmerge "^4.2.2"
    fs-extra "^10.0.0"
    memfs "^3.4.1"
    minimatch "^3.0.4"
    node-abort-controller "^3.0.1"
    schema-utils "^3.1.1"
    semver "^7.3.5"
    tapable "^2.2.1"

form-data@^4.0.4:
  version "4.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/form-data/-/form-data-4.0.4.tgz"
  integrity sha1-eEzczgZpqdaOlNEaxO6pgIjt0sQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fresh@0.5.2:
  version "0.5.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fresh/-/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fs-monkey/-/fs-monkey-1.0.6.tgz"
  integrity sha1-jq0IKVPojZks8/+ET6qQeyZ1baI=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.3, fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/fsevents/-/fsevents-2.3.3.tgz"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/function.prototype.name/-/function.prototype.name-1.1.8.tgz"
  integrity sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/get-symbol-description/-/get-symbol-description-1.1.0.tgz"
  integrity sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1, glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^10.3.10:
  version "10.4.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob/-/glob-10.4.5.tgz"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^10.4.5:
  version "10.4.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob/-/glob-10.4.5.tgz"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.0.3, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/glob/-/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^14.0.0:
  version "14.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/globals/-/globals-14.0.0.tgz"
  integrity sha1-iY10E8Kbq89rr+Vvyt3thYrack4=

globalthis@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/globalthis/-/globalthis-1.0.4.tgz"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^6.1.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/globby/-/globby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/gopd/-/gopd-1.2.0.tgz"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/handle-thing/-/handle-thing-2.0.1.tgz"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

handlebars@^4.7.8:
  version "4.7.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/handlebars/-/handlebars-4.7.8.tgz"
  integrity sha1-QcQsGLG+I2VDkYjHfGr65xwM2ek=
  dependencies:
    minimist "^1.2.5"
    neo-async "^2.6.2"
    source-map "^0.6.1"
    wordwrap "^1.0.0"
  optionalDependencies:
    uglify-js "^3.1.4"

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/has-bigints/-/has-bigints-1.1.0.tgz"
  integrity sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/has-proto/-/has-proto-1.2.0.tgz"
  integrity sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/hasown/-/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/he/-/he-1.2.0.tgz"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hoist-non-react-statics@^3.3.0:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/hpack.js/-/hpack.js-2.1.6.tgz"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-encoding-sniffer@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/html-encoding-sniffer/-/html-encoding-sniffer-4.0.0.tgz"
  integrity sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=
  dependencies:
    whatwg-encoding "^3.1.1"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  integrity sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-parse-stringify@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz"
  integrity sha1-38EBc0fOn3fIFBpQfyMwQMWcVdI=
  dependencies:
    void-elements "3.1.0"

html-webpack-plugin@^5.6.3:
  version "5.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/html-webpack-plugin/-/html-webpack-plugin-5.6.3.tgz"
  integrity sha1-oxFF8P7kGE1Tp5T5UTFH3x5lNoU=
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/htmlparser2/-/htmlparser2-6.1.0.tgz"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-deceiver/-/http-deceiver-1.2.7.tgz"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-errors/-/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha1-t3dKFIbvc892Z6ya4IWMASxXudM=
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-parser-js/-/http-parser-js-0.5.8.tgz"
  integrity sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=

http-proxy-agent@^7.0.2:
  version "7.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  integrity sha1-mosfJGhmwChQlIZYX2K48sGMJw4=
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

http-proxy-middleware@^2.0.9:
  version "2.0.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-proxy-middleware/-/http-proxy-middleware-2.0.9.tgz"
  integrity sha1-6eY9aK+qTu49FH85FJq4TAwoFe8=
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/http-proxy/-/http-proxy-1.18.1.tgz"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

https-proxy-agent@^7.0.6:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

hyperdyperid@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/hyperdyperid/-/hyperdyperid-1.2.0.tgz"
  integrity sha1-WWaNMjrakiKNKoadPkdNWjO2nms=

i18next@^25.3.1, "i18next@>= 25.4.1":
  version "25.4.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/i18next/-/i18next-25.4.2.tgz"
  integrity sha1-l6LGXWxjO3aLlQVZKML2CGpnLwg=
  dependencies:
    "@babel/runtime" "^7.27.6"

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@0.6.3:
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/icss-utils/-/icss-utils-5.1.0.tgz"
  integrity sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ignore/-/ignore-5.3.2.tgz"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0:
  version "7.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ignore/-/ignore-7.0.5.tgz"
  integrity sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2, import-local@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/import-local/-/import-local-3.2.0.tgz"
  integrity sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/inherits/-/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/inherits/-/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/internal-slot/-/internal-slot-1.1.0.tgz"
  integrity sha1-HqyRdilH0vcFa8g42T4TsulgSWE=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

interpret@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/interpret/-/interpret-3.1.1.tgz"
  integrity sha1-W+DO7WfKecbEvFzw1+6EPc6hEMQ=

ipaddr.js@^2.1.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ipaddr.js/-/ipaddr.js-2.2.0.tgz"
  integrity sha1-0z+nusKE9N56+UljjJ1oFXxrkug=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-array-buffer/-/is-array-buffer-3.0.5.tgz"
  integrity sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-async-function@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-async-function/-/is-async-function-2.0.0.tgz"
  integrity sha1-jkQY79Pl06brsBZMBe9a+2mqlkY=
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-bigint/-/is-bigint-1.1.0.tgz"
  integrity sha1-3aejRF31ekJYPbQihoLrp8QXBnI=
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-boolean-object/-/is-boolean-object-1.2.1.tgz"
  integrity sha1-wg0MZUvgXaT7wjxWJjXAGek9r4k=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.13.0, is-core-module@^2.16.0, is-core-module@^2.16.1:
  version "2.16.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-data-view/-/is-data-view-1.0.2.tgz"
  integrity sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-date-object/-/is-date-object-1.1.0.tgz"
  integrity sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-docker@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-docker/-/is-docker-3.0.0.tgz"
  integrity sha1-kAk6oxBid9inelkQ265xdH4VogA=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz"
  integrity sha1-7v3NxslN3QZ02chYh7+T+USpfJA=
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-generator-function@^1.0.10:
  version "1.0.10"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-generator-function/-/is-generator-function-1.0.10.tgz"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-inside-container@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-inside-container/-/is-inside-container-1.0.0.tgz"
  integrity sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=
  dependencies:
    is-docker "^3.0.0"

is-map@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-map/-/is-map-2.0.3.tgz"
  integrity sha1-7elrf+HicLPERl46RlZYdkkm1i4=

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-network-error@^1.0.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-network-error/-/is-network-error-1.1.0.tgz"
  integrity sha1-0mp2DjdwIm0RwWkFLyZqSAPZyZc=

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-number-object/-/is-number-object-1.1.1.tgz"
  integrity sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-number/-/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz"
  integrity sha1-v+Lcomxp85cmWkAJljYCk1oFOss=
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-path-inside/-/is-path-inside-2.1.0.tgz"
  integrity sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=
  dependencies:
    path-is-inside "^1.0.2"

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-regex/-/is-regex-1.2.1.tgz"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-set/-/is-set-2.0.3.tgz"
  integrity sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz"
  integrity sha1-m2eES9m38ka6BwjDqT40Jpx3T28=
  dependencies:
    call-bound "^1.0.3"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-string/-/is-string-1.1.1.tgz"
  integrity sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-symbol/-/is-symbol-1.1.1.tgz"
  integrity sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-weakmap/-/is-weakmap-2.0.2.tgz"
  integrity sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=

is-weakref@^1.0.2, is-weakref@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-weakref/-/is-weakref-1.1.1.tgz"
  integrity sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-weakset/-/is-weakset-2.0.4.tgz"
  integrity sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-wsl@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/is-wsl/-/is-wsl-3.1.0.tgz"
  integrity sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=
  dependencies:
    is-inside-container "^1.0.0"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/isarray/-/isarray-2.0.5.tgz"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/isexe/-/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/isobject/-/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  integrity sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=

istanbul-lib-instrument@^6.0.0, istanbul-lib-instrument@^6.0.2:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz"
  integrity sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  integrity sha1-kIMFusmlvRdaxqdEier9D8JEWn0=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^5.0.0:
  version "5.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz"
  integrity sha1-rK75SN93R8jrX78SZcuYD2NTpEE=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.23"
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"

istanbul-reports@^3.1.3:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/istanbul-reports/-/istanbul-reports-3.2.0.tgz"
  integrity sha1-y0U1FitXhKpiPO4hpyUs8sgHrJM=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/iterator.prototype/-/iterator.prototype-1.1.5.tgz"
  integrity sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jest-changed-files@30.0.5:
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-changed-files/-/jest-changed-files-30.0.5.tgz"
  integrity sha1-7ESPg72cqolN19qHB/IHw1ahmSQ=
  dependencies:
    execa "^5.1.1"
    jest-util "30.0.5"
    p-limit "^3.1.0"

jest-circus@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-circus/-/jest-circus-30.1.3.tgz"
  integrity sha1-fuAInyKys+cqsErujgN8Nkptc9E=
  dependencies:
    "@jest/environment" "30.1.2"
    "@jest/expect" "30.1.2"
    "@jest/test-result" "30.1.3"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    co "^4.6.0"
    dedent "^1.6.0"
    is-generator-fn "^2.1.0"
    jest-each "30.1.0"
    jest-matcher-utils "30.1.2"
    jest-message-util "30.1.0"
    jest-runtime "30.1.3"
    jest-snapshot "30.1.2"
    jest-util "30.0.5"
    p-limit "^3.1.0"
    pretty-format "30.0.5"
    pure-rand "^7.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.6"

jest-cli@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-cli/-/jest-cli-30.1.3.tgz"
  integrity sha1-P7jeqIiGN565Wgj5VL/C7Repvk8=
  dependencies:
    "@jest/core" "30.1.3"
    "@jest/test-result" "30.1.3"
    "@jest/types" "30.0.5"
    chalk "^4.1.2"
    exit-x "^0.2.2"
    import-local "^3.2.0"
    jest-config "30.1.3"
    jest-util "30.0.5"
    jest-validate "30.1.0"
    yargs "^17.7.2"

jest-config@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-config/-/jest-config-30.1.3.tgz"
  integrity sha1-ELz0zZeRGb+sahMPt52DcFfOM9Q=
  dependencies:
    "@babel/core" "^7.27.4"
    "@jest/get-type" "30.1.0"
    "@jest/pattern" "30.0.1"
    "@jest/test-sequencer" "30.1.3"
    "@jest/types" "30.0.5"
    babel-jest "30.1.2"
    chalk "^4.1.2"
    ci-info "^4.2.0"
    deepmerge "^4.3.1"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    jest-circus "30.1.3"
    jest-docblock "30.0.1"
    jest-environment-node "30.1.2"
    jest-regex-util "30.0.1"
    jest-resolve "30.1.3"
    jest-runner "30.1.3"
    jest-util "30.0.5"
    jest-validate "30.1.0"
    micromatch "^4.0.8"
    parse-json "^5.2.0"
    pretty-format "30.0.5"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@30.1.2:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-diff/-/jest-diff-30.1.2.tgz"
  integrity sha1-j/Qhfltj/vSaWzdGKZnY9SmaTrQ=
  dependencies:
    "@jest/diff-sequences" "30.0.1"
    "@jest/get-type" "30.1.0"
    chalk "^4.1.2"
    pretty-format "30.0.5"

jest-docblock@30.0.1:
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-docblock/-/jest-docblock-30.0.1.tgz"
  integrity sha1-VF/1ny+oiZa9Rw26fTeYqEIRgLE=
  dependencies:
    detect-newline "^3.1.0"

jest-each@30.1.0:
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-each/-/jest-each-30.1.0.tgz"
  integrity sha1-IodW1eqeTctGL8LpCkTsJ91ILSM=
  dependencies:
    "@jest/get-type" "30.1.0"
    "@jest/types" "30.0.5"
    chalk "^4.1.2"
    jest-util "30.0.5"
    pretty-format "30.0.5"

jest-environment-jsdom@^30.0.4:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-environment-jsdom/-/jest-environment-jsdom-30.1.2.tgz"
  integrity sha1-MHVuc7m3wQ676cBm8w3Rr2cOucE=
  dependencies:
    "@jest/environment" "30.1.2"
    "@jest/environment-jsdom-abstract" "30.1.2"
    "@types/jsdom" "^21.1.7"
    "@types/node" "*"
    jsdom "^26.1.0"

jest-environment-node@30.1.2:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-environment-node/-/jest-environment-node-30.1.2.tgz"
  integrity sha1-ri8gRC+KvDxrIBINx4n6OPr/Vo8=
  dependencies:
    "@jest/environment" "30.1.2"
    "@jest/fake-timers" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    jest-mock "30.0.5"
    jest-util "30.0.5"
    jest-validate "30.1.0"

jest-haste-map@30.1.0:
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-haste-map/-/jest-haste-map-30.1.0.tgz"
  integrity sha1-5U2E4H+sFeo6mJA7c1BI4219LtM=
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    anymatch "^3.1.3"
    fb-watchman "^2.0.2"
    graceful-fs "^4.2.11"
    jest-regex-util "30.0.1"
    jest-util "30.0.5"
    jest-worker "30.1.0"
    micromatch "^4.0.8"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.3"

jest-junit@^16.0.0:
  version "16.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-junit/-/jest-junit-16.0.0.tgz"
  integrity sha1-2DjoxWHPn91+tU9jAgd37uQTZ4U=
  dependencies:
    mkdirp "^1.0.4"
    strip-ansi "^6.0.1"
    uuid "^8.3.2"
    xml "^1.0.1"

jest-leak-detector@30.1.0:
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-leak-detector/-/jest-leak-detector-30.1.0.tgz"
  integrity sha1-i4bnxfHj5PKjLZMOx2kQOtCYWHQ=
  dependencies:
    "@jest/get-type" "30.1.0"
    pretty-format "30.0.5"

jest-localstorage-mock@^2.4.26:
  version "2.4.26"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-localstorage-mock/-/jest-localstorage-mock-2.4.26.tgz"
  integrity sha1-fVf7NVXy7Vt+0W/YQj/YH5Xp6Ns=

jest-matcher-utils@30.1.2:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-matcher-utils/-/jest-matcher-utils-30.1.2.tgz"
  integrity sha1-PxtjlJ90ACWv90DGxqG2U643D7s=
  dependencies:
    "@jest/get-type" "30.1.0"
    chalk "^4.1.2"
    jest-diff "30.1.2"
    pretty-format "30.0.5"

jest-message-util@30.1.0:
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-message-util/-/jest-message-util-30.1.0.tgz"
  integrity sha1-ZTqbsaMzBu3fE0Vc4GZrpiG3Z8Q=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@jest/types" "30.0.5"
    "@types/stack-utils" "^2.0.3"
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    micromatch "^4.0.8"
    pretty-format "30.0.5"
    slash "^3.0.0"
    stack-utils "^2.0.6"

jest-mock@30.0.5:
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-mock/-/jest-mock-30.0.5.tgz"
  integrity sha1-70N+iSElYN05UZgRVVAIUDhXC90=
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    jest-util "30.0.5"

jest-pnp-resolver@^1.2.3:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  integrity sha1-kwsVRhZNStWTfVVA5xHU041MrS4=

jest-regex-util@30.0.1:
  version "30.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-regex-util/-/jest-regex-util-30.0.1.tgz"
  integrity sha1-8Xwd45WLZ9/khTVPWhAJMpjypJs=

jest-resolve-dependencies@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-resolve-dependencies/-/jest-resolve-dependencies-30.1.3.tgz"
  integrity sha1-BLvpXJ9K9RBG3elAaY1xIbSdAWc=
  dependencies:
    jest-regex-util "30.0.1"
    jest-snapshot "30.1.2"

jest-resolve@*, jest-resolve@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-resolve/-/jest-resolve-30.1.3.tgz"
  integrity sha1-zBAZsoN0ynvPfljVekMARJ85DsU=
  dependencies:
    chalk "^4.1.2"
    graceful-fs "^4.2.11"
    jest-haste-map "30.1.0"
    jest-pnp-resolver "^1.2.3"
    jest-util "30.0.5"
    jest-validate "30.1.0"
    slash "^3.0.0"
    unrs-resolver "^1.7.11"

jest-runner@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-runner/-/jest-runner-30.1.3.tgz"
  integrity sha1-MlOg+quPQEqp4AEJEeisuvIghls=
  dependencies:
    "@jest/console" "30.1.2"
    "@jest/environment" "30.1.2"
    "@jest/test-result" "30.1.3"
    "@jest/transform" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    emittery "^0.13.1"
    exit-x "^0.2.2"
    graceful-fs "^4.2.11"
    jest-docblock "30.0.1"
    jest-environment-node "30.1.2"
    jest-haste-map "30.1.0"
    jest-leak-detector "30.1.0"
    jest-message-util "30.1.0"
    jest-resolve "30.1.3"
    jest-runtime "30.1.3"
    jest-util "30.0.5"
    jest-watcher "30.1.3"
    jest-worker "30.1.0"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-runtime/-/jest-runtime-30.1.3.tgz"
  integrity sha1-vKfLSNU8W1riE5nnpl4hJx9QAAQ=
  dependencies:
    "@jest/environment" "30.1.2"
    "@jest/fake-timers" "30.1.2"
    "@jest/globals" "30.1.2"
    "@jest/source-map" "30.0.1"
    "@jest/test-result" "30.1.3"
    "@jest/transform" "30.1.2"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    cjs-module-lexer "^2.1.0"
    collect-v8-coverage "^1.0.2"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    jest-haste-map "30.1.0"
    jest-message-util "30.1.0"
    jest-mock "30.0.5"
    jest-regex-util "30.0.1"
    jest-resolve "30.1.3"
    jest-snapshot "30.1.2"
    jest-util "30.0.5"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@30.1.2:
  version "30.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-snapshot/-/jest-snapshot-30.1.2.tgz"
  integrity sha1-QAGpTYOUuwd6HJYkbwEHyBq6TxI=
  dependencies:
    "@babel/core" "^7.27.4"
    "@babel/generator" "^7.27.5"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/plugin-syntax-typescript" "^7.27.1"
    "@babel/types" "^7.27.3"
    "@jest/expect-utils" "30.1.2"
    "@jest/get-type" "30.1.0"
    "@jest/snapshot-utils" "30.1.2"
    "@jest/transform" "30.1.2"
    "@jest/types" "30.0.5"
    babel-preset-current-node-syntax "^1.1.0"
    chalk "^4.1.2"
    expect "30.1.2"
    graceful-fs "^4.2.11"
    jest-diff "30.1.2"
    jest-matcher-utils "30.1.2"
    jest-message-util "30.1.0"
    jest-util "30.0.5"
    pretty-format "30.0.5"
    semver "^7.7.2"
    synckit "^0.11.8"

"jest-util@^29.0.0 || ^30.0.0", jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-util/-/jest-util-29.7.0.tgz"
  integrity sha1-I8K2K/sivoK0TemAVYAv83EPwLw=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-util@30.0.5:
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-util/-/jest-util-30.0.5.tgz"
  integrity sha1-A104DGYK1fF0jf9xxBBTOOBfhmk=
  dependencies:
    "@jest/types" "30.0.5"
    "@types/node" "*"
    chalk "^4.1.2"
    ci-info "^4.2.0"
    graceful-fs "^4.2.11"
    picomatch "^4.0.2"

jest-validate@30.1.0:
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-validate/-/jest-validate-30.1.0.tgz"
  integrity sha1-WFqubJ7hrBONusvs6KeDjKd3PmA=
  dependencies:
    "@jest/get-type" "30.1.0"
    "@jest/types" "30.0.5"
    camelcase "^6.3.0"
    chalk "^4.1.2"
    leven "^3.1.0"
    pretty-format "30.0.5"

jest-watcher@30.1.3:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-watcher/-/jest-watcher-30.1.3.tgz"
  integrity sha1-LzgdpcLHakbEa6IQjmYHxYVCHcA=
  dependencies:
    "@jest/test-result" "30.1.3"
    "@jest/types" "30.0.5"
    "@types/node" "*"
    ansi-escapes "^4.3.2"
    chalk "^4.1.2"
    emittery "^0.13.1"
    jest-util "30.0.5"
    string-length "^4.0.2"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-worker/-/jest-worker-29.7.0.tgz"
  integrity sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@30.1.0:
  version "30.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest-worker/-/jest-worker-30.1.0.tgz"
  integrity sha1-qJw2dyvkSdS9tgaX+2laFnOxKsI=
  dependencies:
    "@types/node" "*"
    "@ungap/structured-clone" "^1.3.0"
    jest-util "30.0.5"
    merge-stream "^2.0.0"
    supports-color "^8.1.1"

"jest@^29.0.0 || ^30.0.0", jest@^30.0.4:
  version "30.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jest/-/jest-30.1.3.tgz"
  integrity sha1-yWIpD2XDLUSgYk94Wy14CDVSWiM=
  dependencies:
    "@jest/core" "30.1.3"
    "@jest/types" "30.0.5"
    import-local "^3.2.0"
    jest-cli "30.1.3"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsdom@*, jsdom@^26.1.0:
  version "26.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jsdom/-/jsdom-26.1.0.tgz"
  integrity sha1-q18cHK/AS9h4clSQl06l6L8McrM=
  dependencies:
    cssstyle "^4.2.1"
    data-urls "^5.0.0"
    decimal.js "^10.5.0"
    html-encoding-sniffer "^4.0.0"
    http-proxy-agent "^7.0.2"
    https-proxy-agent "^7.0.6"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.16"
    parse5 "^7.2.1"
    rrweb-cssom "^0.8.0"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^5.1.1"
    w3c-xmlserializer "^5.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^3.1.1"
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.1.1"
    ws "^8.18.0"
    xml-name-validator "^5.0.0"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jsesc/-/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

jsesc@~3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jsesc/-/jsesc-3.0.2.tgz"
  integrity sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json5/-/json5-1.0.2.tgz"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/json5/-/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonwebtoken@^9.0.2:
  version "9.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  integrity sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jwa/-/jwa-1.4.1.tgz"
  integrity sha1-dDwymFy56YZVUw1TZBtmyGRbA5o=
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/jws/-/jws-3.2.2.tgz"
  integrity sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

keyv@^4.5.4:
  version "4.5.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/keyv/-/keyv-4.5.4.tgz"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

launch-editor@^2.6.1:
  version "2.9.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/launch-editor/-/launch-editor-2.9.1.tgz"
  integrity sha1-JT8XO9RB40LUNEtNrlgpGrtCUEc=
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

leven@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/leven/-/leven-3.1.0.tgz"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/levn/-/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.includes/-/lodash.includes-4.3.0.tgz"
  integrity sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  integrity sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  integrity sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  integrity sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash.once/-/lodash.once-4.1.1.tgz"
  integrity sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=

lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lodash/-/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lower-case/-/lower-case-2.0.2.tgz"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^10.4.3:
  version "10.4.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lz-string@^1.5.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/lz-string/-/lz-string-1.5.0.tgz"
  integrity sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/make-dir/-/make-dir-4.0.0.tgz"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

make-error@^1.1.1, make-error@^1.3.6:
  version "1.3.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/make-error/-/make-error-1.3.6.tgz"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/makeerror/-/makeerror-1.0.12.tgz"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.4.1:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/memfs/-/memfs-3.6.0.tgz"
  integrity sha1-16IRD4b3ndlQqLbfbVe8mEqhhfY=
  dependencies:
    fs-monkey "^1.0.4"

memfs@^4.6.0:
  version "4.15.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/memfs/-/memfs-4.15.3.tgz"
  integrity sha1-xPmlAn3ArAsAb41aY67hKMgpo30=
  dependencies:
    "@jsonjoy.com/json-pack" "^1.0.3"
    "@jsonjoy.com/util" "^1.3.0"
    tree-dump "^1.0.1"
    tslib "^2.0.0"

merge-descriptors@1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/merge-descriptors/-/merge-descriptors-1.0.3.tgz"
  integrity sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/merge2/-/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/methods/-/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^4.0.2, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

"mime-db@>= 1.43.0 < 2", mime-db@1.52.0:
  version "1.52.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/mime/-/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

min-indent@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/min-indent/-/min-indent-1.0.1.tgz"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/minimist/-/minimist-1.2.8.tgz"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/minipass/-/minipass-7.1.2.tgz"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

mkdirp@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

ms@^2.1.1, ms@^2.1.3, ms@2.1.3:
  version "2.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ms/-/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

ms@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/multicast-dns/-/multicast-dns-7.2.5.tgz"
  integrity sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

nanoid@^3.3.7:
  version "3.3.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/nanoid/-/nanoid-3.3.8.tgz"
  integrity sha1-sb4wML7jaq/xi6yzdeXM5SFoS68=

napi-postinstall@^0.3.0:
  version "0.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/napi-postinstall/-/napi-postinstall-0.3.3.tgz"
  integrity sha1-k9BFxrV2gD6tEmcR0wk5lRmMbrk=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@~0.6.4:
  version "0.6.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/negotiator/-/negotiator-0.6.4.tgz"
  integrity sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/no-case/-/no-case-3.0.4.tgz"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-abort-controller@^3.0.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/node-abort-controller/-/node-abort-controller-3.1.1.tgz"
  integrity sha1-qUN36WSpo3rDl22EjLXHZYM7hUg=

node-forge@^1:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/node-forge/-/node-forge-1.3.1.tgz"
  integrity sha1-vo2iryQ7JBfV9kancGY6krfp3tM=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/node-releases/-/node-releases-2.0.19.tgz"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/nth-check/-/nth-check-2.1.1.tgz"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

nwsapi@^2.2.16:
  version "2.2.21"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/nwsapi/-/nwsapi-2.2.21.tgz"
  integrity sha1-jfd5cHk1Ct2iCJENjDP8TC11IMM=

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3, object-inspect@^1.13.4:
  version "1.13.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object.assign/-/object.assign-4.1.7.tgz"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.9:
  version "1.1.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object.entries/-/object.entries-1.1.9.tgz"
  integrity sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object.fromentries/-/object.fromentries-2.0.8.tgz"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.groupby@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object.groupby/-/object.groupby-1.0.3.tgz"
  integrity sha1-mxJcNiOBKfb3thlUoecXYUjVAC4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"

object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/object.values/-/object.values-1.2.1.tgz"
  integrity sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/obuf/-/obuf-1.1.2.tgz"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@^2.4.1, on-finished@2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/onetime/-/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^10.0.3:
  version "10.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/open/-/open-10.1.0.tgz"
  integrity sha1-p3lebl1Rmr5ChtmTe7JLURIlmOE=
  dependencies:
    default-browser "^5.2.1"
    define-lazy-prop "^3.0.0"
    is-inside-container "^1.0.0"
    is-wsl "^3.1.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/optionator/-/optionator-0.9.4.tgz"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

own-keys@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/own-keys/-/own-keys-1.0.1.tgz"
  integrity sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-map/-/p-map-2.1.0.tgz"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-retry@^6.2.0:
  version "6.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-retry/-/p-retry-6.2.1.tgz"
  integrity sha1-gYKPjcYcbvWoAFhUkVcsyYknA68=
  dependencies:
    "@types/retry" "0.12.2"
    is-network-error "^1.0.0"
    retry "^0.13.1"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/p-try/-/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/param-case/-/param-case-3.0.4.tgz"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5@^7.0.0, parse5@^7.2.1:
  version "7.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/parse5/-/parse5-7.3.0.tgz"
  integrity sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=
  dependencies:
    entities "^6.0.0"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pascal-case/-/pascal-case-3.1.2.tgz"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-is-inside/-/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-key/-/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@0.1.12:
  version "0.1.12"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-to-regexp/-/path-to-regexp-0.1.12.tgz"
  integrity sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/path-type/-/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

phlex-core-ui@1.1.972:
  version "1.1.972"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/phlex-core-ui/-/phlex-core-ui-1.1.972.tgz"
  integrity sha1-qUVht/uziS9J8IGTuXcdFTPyh+o=

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

"picomatch@^3 || ^4", picomatch@^4.0.2:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/picomatch/-/picomatch-4.0.3.tgz"
  integrity sha1-eWx2E20e6tcV2x57rXhd7daVoEI=

pify@^2.0.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pify/-/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pify/-/pify-4.0.1.tgz"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pinkie/-/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pirates@^4.0.7:
  version "4.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pirates/-/pirates-4.0.7.tgz"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

possible-typed-array-names@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz"
  integrity sha1-ibtjxvraLD6QrcSmR77us5zHv48=

postcss-modules-extract-imports@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz"
  integrity sha1-tEl8uFqcDEtaq+t1m7JejYnxUAI=

postcss-modules-local-by-default@^4.0.5:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.2.0.tgz"
  integrity sha1-0VD0ODeDHa4l5AhVluhPb11uw2g=
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^7.0.0"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.2.0:
  version "3.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz"
  integrity sha1-G7zN3LOY8delEeCi0dBHcYr0B4w=
  dependencies:
    postcss-selector-parser "^7.0.0"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  integrity sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=
  dependencies:
    icss-utils "^5.0.0"

postcss-selector-parser@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss-selector-parser/-/postcss-selector-parser-7.0.0.tgz"
  integrity sha1-Qb2LVvF3wJPKSUNfZXMb7+Jda5w=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.2, postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.1.0, postcss@^8.4.33, postcss@8.4.49:
  version "8.4.49"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/postcss/-/postcss-8.4.49.tgz"
  integrity sha1-TqR5BIqwWas65h0IIZD6v9mU/hk=
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^3.6.2, prettier@>=3.0.0:
  version "3.6.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/prettier/-/prettier-3.6.2.tgz"
  integrity sha1-zNoCoQA+u7K/2m+DoHSXj2CLk5M=

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pretty-error/-/pretty-error-4.0.0.tgz"
  integrity sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

pretty-format@^27.0.2:
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pretty-format/-/pretty-format-27.5.1.tgz"
  integrity sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

pretty-format@^30.0.0:
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pretty-format/-/pretty-format-30.0.5.tgz"
  integrity sha1-4AFknUcoADlsEgloRIPhik0lA2A=
  dependencies:
    "@jest/schemas" "30.0.5"
    ansi-styles "^5.2.0"
    react-is "^18.3.1"

pretty-format@30.0.5:
  version "30.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pretty-format/-/pretty-format-30.0.5.tgz"
  integrity sha1-4AFknUcoADlsEgloRIPhik0lA2A=
  dependencies:
    "@jest/schemas" "30.0.5"
    ansi-styles "^5.2.0"
    react-is "^18.3.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

prop-types@^15.6.0, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.5:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/property-expr/-/property-expr-2.0.6.tgz"
  integrity sha1-93vADVkopsdIQUrRKILoPySuweg=

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

punycode@^2.1.0, punycode@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/punycode/-/punycode-2.3.1.tgz"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

pure-rand@^7.0.0:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/pure-rand/-/pure-rand-7.0.1.tgz"
  integrity sha1-b1OlqePkpHRFgir5aCHKUJ7TdWY=

qs@6.13.0:
  version "6.13.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/qs/-/qs-6.13.0.tgz"
  integrity sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=
  dependencies:
    side-channel "^1.0.6"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

"react-dom@^16.8.2 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom@^18 || ^19", "react-dom@^18.0.0 || ^19.0.0", "react-dom@>= 16.8.0", react-dom@>=16.6.0, react-dom@>=18, react-dom@18.3.1:
  version "18.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-dom/-/react-dom-18.3.1.tgz"
  integrity sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-hook-form@^7.55.0, react-hook-form@^7.59.0:
  version "7.62.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-hook-form/-/react-hook-form-7.62.0.tgz"
  integrity sha1-LYHhPCxrbWNlSORAgYNBynUyGNA=

react-i18next@^15.6.0:
  version "15.7.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-i18next/-/react-i18next-15.7.3.tgz"
  integrity sha1-LrojUkff8Mv58DOOKrheEOEnqlQ=
  dependencies:
    "@babel/runtime" "^7.27.6"
    html-parse-stringify "^3.0.1"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-is/-/react-is-16.13.1.tgz"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^17.0.1:
  version "17.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-is/-/react-is-17.0.2.tgz"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

react-is@^18.3.1:
  version "18.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-is/-/react-is-18.3.1.tgz"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-router-dom@7.6.3:
  version "7.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-router-dom/-/react-router-dom-7.6.3.tgz"
  integrity sha1-RWhucbuVjPgN2Tyjq/QRH+tO3TU=
  dependencies:
    react-router "7.6.3"

react-router@7.6.3:
  version "7.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-router/-/react-router-7.6.3.tgz"
  integrity sha1-ek6ltHm2bSxJqPAAgSsjGbTQpto=
  dependencies:
    cookie "^1.0.1"
    set-cookie-parser "^2.6.0"

react-toastify@*, react-toastify@^11.0.5:
  version "11.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-toastify/-/react-toastify-11.0.5.tgz"
  integrity sha1-zkxC0Q7rQzmIqyJk0+RFxOnRMxM=
  dependencies:
    clsx "^2.1.1"

react-transition-group@^4.4.2:
  version "4.4.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

"react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.2 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^18 || ^19", "react@^18.0.0 || ^19.0.0", react@^18.3.1, "react@>= 16.8.0", react@>=16.6.0, react@>=18, react@18.3.1:
  version "18.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/react/-/react-18.3.1.tgz"
  integrity sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=
  dependencies:
    loose-envify "^1.1.0"

readable-stream@^2.0.1:
  version "2.3.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^4.0.1:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/readdirp/-/readdirp-4.1.2.tgz"
  integrity sha1-64WAFDX78qfuWPGeCSGwaPxplI0=

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.8.0:
  version "0.8.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/rechoir/-/rechoir-0.8.0.tgz"
  integrity sha1-Sfhm4NMhRhQto62PDv81KzIV/yI=
  dependencies:
    resolve "^1.20.0"

redent@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/redent/-/redent-3.0.0.tgz"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/reflect.getprototypeof/-/reflect.getprototypeof-1.0.9.tgz"
  integrity sha1-yQXzOGAI3pWmIxXz6oYwQEvhni8=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    dunder-proto "^1.0.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz"
  integrity sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regexp.prototype.flags@^1.5.3, regexp.prototype.flags@^1.5.4:
  version "1.5.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  integrity sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/regexpu-core/-/regexpu-core-6.2.0.tgz"
  integrity sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/regjsgen/-/regjsgen-0.8.0.tgz"
  integrity sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/regjsparser/-/regjsparser-0.12.0.tgz"
  integrity sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=
  dependencies:
    jsesc "~3.0.2"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/relateurl/-/relateurl-0.2.7.tgz"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/renderkid/-/renderkid-3.0.0.tgz"
  integrity sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

requireindex@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/requireindex/-/requireindex-1.2.0.tgz"
  integrity sha1-NGPNsi7hUZAmNapslTXU3pwu8e8=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve@^1.20.0, resolve@^1.22.10, resolve@^1.22.4:
  version "1.22.10"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/resolve/-/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/resolve/-/resolve-2.0.0-next.5.tgz"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/retry/-/retry-0.13.1.tgz"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/reusify/-/reusify-1.1.0.tgz"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/rimraf/-/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rrweb-cssom@^0.8.0:
  version "0.8.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/rrweb-cssom/-/rrweb-cssom-0.8.0.tgz"
  integrity sha1-MCHRtDUvvzthSq7tC8DVc5q+C8I=

run-applescript@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/run-applescript/-/run-applescript-7.0.0.tgz"
  integrity sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/safe-array-concat/-/safe-array-concat-1.1.3.tgz"
  integrity sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@>=5.1.0, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/safe-push-apply/-/safe-push-apply-1.0.0.tgz"
  integrity sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/safe-regex-test/-/safe-regex-test-1.1.0.tgz"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

saxes@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/saxes/-/saxes-6.0.0.tgz"
  integrity sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/scheduler/-/scheduler-0.23.2.tgz"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

schema-utils@^3.1.1:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha1-9QqIh3w8AWUqFbYirp6Xld96YP4=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0, schema-utils@^4.2.0, schema-utils@^4.3.0, schema-utils@^4.3.2:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/schema-utils/-/schema-utils-4.3.2.tgz"
  integrity sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/select-hose/-/select-hose-2.0.0.tgz"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/selfsigned/-/selfsigned-2.4.1.tgz"
  integrity sha1-Vg2QVlRCo+01tnQDTOxOldzrSuA=
  dependencies:
    "@types/node-forge" "^1.3.0"
    node-forge "^1"

semver@^6.3.1:
  version "6.3.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/semver/-/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.5:
  version "7.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/semver/-/semver-7.6.3.tgz"
  integrity sha1-mA97VVC8F1+03AlAMIVif56zMUM=

semver@^7.5.3:
  version "7.7.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/semver/-/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

semver@^7.5.4:
  version "7.6.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/semver/-/semver-7.6.3.tgz"
  integrity sha1-mA97VVC8F1+03AlAMIVif56zMUM=

semver@^7.6.0:
  version "7.7.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/semver/-/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

semver@^7.7.2:
  version "7.7.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/semver/-/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

send@0.19.0:
  version "0.19.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/send/-/send-0.19.0.tgz"
  integrity sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  integrity sha1-3voeBVyDv21Z6oBdjahiJU62psI=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/serve-index/-/serve-index-1.9.1.tgz"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.16.2:
  version "1.16.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/serve-static/-/serve-static-1.16.2.tgz"
  integrity sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

set-cookie-parser@^2.6.0:
  version "2.7.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz"
  integrity sha1-MBbxUAciAt++kPre4FNXPMidKUM=

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/set-function-name/-/set-function-name-2.0.2.tgz"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/set-proto/-/set-proto-1.0.0.tgz"
  integrity sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/setprototypeof/-/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/shallow-clone/-/shallow-clone-3.0.1.tgz"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shallowequal@1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/shallowequal/-/shallowequal-1.1.0.tgz"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.8.1:
  version "1.8.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/shell-quote/-/shell-quote-1.8.2.tgz"
  integrity sha1-0tg+BXlZ1T7CYTEenpuPUdyyk0o=

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6, side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/side-channel/-/side-channel-1.1.0.tgz"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/slash/-/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

sockjs@^0.3.24:
  version "0.3.24"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/sockjs/-/sockjs-0.3.24.tgz"
  integrity sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/source-map-support/-/source-map-support-0.5.13.tgz"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/source-map/-/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/spdy-transport/-/spdy-transport-3.0.0.tgz"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/spdy/-/spdy-4.0.2.tgz"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stack-utils@^2.0.6:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/stack-utils/-/stack-utils-2.0.6.tgz"
  integrity sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=
  dependencies:
    escape-string-regexp "^2.0.0"

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/statuses/-/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

statuses@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/statuses/-/statuses-2.0.1.tgz"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

stop-iteration-iterator@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz"
  integrity sha1-9IH/cKVI9hJNAxLDqhTL+nqlQq0=
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

string-length@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string-length/-/string-length-4.0.2.tgz"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string-width/-/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string-width/-/string-width-5.1.2.tgz"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz"
  integrity sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz"
  integrity sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz"
  integrity sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz"
  integrity sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-bom/-/strip-bom-4.0.0.tgz"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-indent/-/strip-indent-3.0.0.tgz"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-loader@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/style-loader/-/style-loader-4.0.0.tgz"
  integrity sha1-DqluRo9DxpYAAR4FicsFxE87F6U=

styled-components@^6.1.16, styled-components@6.1.16:
  version "6.1.16"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/styled-components/-/styled-components-6.1.16.tgz"
  integrity sha1-zTJ8+gkoKQItPSyskbwiC1bcNy0=
  dependencies:
    "@emotion/is-prop-valid" "1.2.2"
    "@emotion/unitless" "0.8.1"
    "@types/stylis" "4.2.5"
    css-to-react-native "3.2.0"
    csstype "3.1.3"
    postcss "8.4.49"
    shallowequal "1.1.0"
    stylis "4.3.2"
    tslib "2.6.2"

stylis@4.3.2:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/stylis/-/stylis-4.3.2.tgz"
  integrity sha1-j3a3B3fdU+tmnG9YyZe/Cply5EQ=

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.1.1:
  version "8.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/symbol-tree/-/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

synckit@^0.11.7, synckit@^0.11.8:
  version "0.11.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/synckit/-/synckit-0.11.11.tgz"
  integrity sha1-wLYZzyWKl/qiCRVdnNFpm1yZjLA=
  dependencies:
    "@pkgr/core" "^0.2.9"

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tapable/-/tapable-2.2.1.tgz"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

terser-webpack-plugin@^5.3.11:
  version "5.3.11"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/terser-webpack-plugin/-/terser-webpack-plugin-5.3.11.tgz"
  integrity sha1-k8IfRMqGY0JXysF2+IT5Qre6ODI=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    jest-worker "^27.4.5"
    schema-utils "^4.3.0"
    serialize-javascript "^6.0.2"
    terser "^5.31.1"

terser@^5.10.0, terser@^5.31.1:
  version "5.37.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/terser/-/terser-5.37.0.tgz"
  integrity sha1-OKpm0c/EPQY4+rVOQ/+KT3KiG6M=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/test-exclude/-/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

thingies@^1.20.0:
  version "1.21.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/thingies/-/thingies-1.21.0.tgz"
  integrity sha1-6A++WP1v2qq4+tm2e9ClyUPERcE=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/thunky/-/thunky-1.1.0.tgz"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

tiny-case@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tiny-case/-/tiny-case-1.0.3.tgz"
  integrity sha1-2YDWa8crXVqcqG+3yf/bnImN3QM=

tinyglobby@^0.2.12:
  version "0.2.14"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tinyglobby/-/tinyglobby-0.2.14.tgz"
  integrity sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tldts-core@^6.1.86:
  version "6.1.86"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tldts-core/-/tldts-core-6.1.86.tgz"
  integrity sha1-qT5u2dUFy1TFQs5D/rFMc5EyZdg=

tldts@^6.1.32:
  version "6.1.86"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tldts/-/tldts-6.1.86.tgz"
  integrity sha1-CH4FVbMblyXuSMp+d+3FYRXNgvc=
  dependencies:
    tldts-core "^6.1.86"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tmpl/-/tmpl-1.0.5.tgz"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/toposort/-/toposort-2.0.2.tgz"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

tough-cookie@^5.1.1:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tough-cookie/-/tough-cookie-5.1.2.tgz"
  integrity sha1-Ztd0tKHZ4S3HUIlyWvOsdewxvtc=
  dependencies:
    tldts "^6.1.32"

tr46@^5.1.0:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tr46/-/tr46-5.1.1.tgz"
  integrity sha1-lq6GfN24/bZKScwwWajUKLzyOMo=
  dependencies:
    punycode "^2.3.1"

tree-dump@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tree-dump/-/tree-dump-1.0.2.tgz"
  integrity sha1-xGDVkhyusZe95x0OmntHmEjFuKw=

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ts-api-utils/-/ts-api-utils-2.1.0.tgz"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

ts-jest@^29.4.0:
  version "29.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ts-jest/-/ts-jest-29.4.1.tgz"
  integrity sha1-QtM763Rld1HTFe+5qHH+meO5tRk=
  dependencies:
    bs-logger "^0.2.6"
    fast-json-stable-stringify "^2.1.0"
    handlebars "^4.7.8"
    json5 "^2.2.3"
    lodash.memoize "^4.1.2"
    make-error "^1.3.6"
    semver "^7.7.2"
    type-fest "^4.41.0"
    yargs-parser "^21.1.1"

ts-node@^10.9.2, ts-node@>=9.0.0:
  version "10.9.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths-webpack-plugin@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-4.2.0.tgz"
  integrity sha1-90WajtHdTPZq14eu/D03//PPB/w=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.7.0"
    tapable "^2.2.1"
    tsconfig-paths "^4.1.2"

tsconfig-paths@^3.15.0:
  version "3.15.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz"
  integrity sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tsconfig-paths@^4.1.2:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz"
  integrity sha1-73jhkDkTNEbSRL6sD9ahYy4tEHw=
  dependencies:
    json5 "^2.2.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.7.0:
  version "1.14.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tslib/-/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2, tslib@^2.0.0, tslib@2:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tslib/-/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tslib@^2.0.3:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tslib/-/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tslib@^2.3.1:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tslib/-/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tslib@^2.4.0:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tslib/-/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tslib@2.6.2:
  version "2.6.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/tslib/-/tslib-2.6.2.tgz"
  integrity sha1-cDrClCXns3zW/UVukkBNRtHz5K4=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/type-check/-/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/type-detect/-/type-detect-4.0.8.tgz"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^2.19.0:
  version "2.19.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/type-fest/-/type-fest-2.19.0.tgz"
  integrity sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=

type-fest@^4.41.0:
  version "4.41.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/type-fest/-/type-fest-4.41.0.tgz"
  integrity sha1-auHI5XMSc8K/H1itOcuuLJGkbFg=

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/type-is/-/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz"
  integrity sha1-hAegT314aE89JSqhoUPSt3tBYM4=
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz"
  integrity sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/typed-array-length/-/typed-array-length-1.0.7.tgz"
  integrity sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript@^5, typescript@^5.8.3, typescript@>=2.7, "typescript@>=4.3 <6", typescript@>=4.8.4, "typescript@>=4.8.4 <6.0.0", typescript@>=4.9.5, typescript@>3.6.0:
  version "5.9.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/typescript/-/typescript-5.9.2.tgz"
  integrity sha1-2TRQzd7FFUotXKvjuBArgzFvsqY=

uglify-js@^3.1.4:
  version "3.19.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/uglify-js/-/uglify-js-3.19.3.tgz"
  integrity sha1-gjFem7xvKyWIiFis0f/4RBA1t38=

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unbox-primitive/-/unbox-primitive-1.1.0.tgz"
  integrity sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~7.10.0:
  version "7.10.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/undici-types/-/undici-types-7.10.0.tgz"
  integrity sha1-SsLgWM5WtGKwVuYpzGoCOT0/81A=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  integrity sha1-yzFz/kfKdD4ighbko93EyE1ijMI=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz"
  integrity sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/universalify/-/universalify-2.0.1.tgz"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unrs-resolver@^1.7.11:
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/unrs-resolver/-/unrs-resolver-1.11.1.tgz"
  integrity sha1-vpzYaGyZ71PsuW3ypHPGTTBASKk=
  dependencies:
    napi-postinstall "^0.3.0"
  optionalDependencies:
    "@unrs/resolver-binding-android-arm-eabi" "1.11.1"
    "@unrs/resolver-binding-android-arm64" "1.11.1"
    "@unrs/resolver-binding-darwin-arm64" "1.11.1"
    "@unrs/resolver-binding-darwin-x64" "1.11.1"
    "@unrs/resolver-binding-freebsd-x64" "1.11.1"
    "@unrs/resolver-binding-linux-arm-gnueabihf" "1.11.1"
    "@unrs/resolver-binding-linux-arm-musleabihf" "1.11.1"
    "@unrs/resolver-binding-linux-arm64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-arm64-musl" "1.11.1"
    "@unrs/resolver-binding-linux-ppc64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-riscv64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-riscv64-musl" "1.11.1"
    "@unrs/resolver-binding-linux-s390x-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-x64-gnu" "1.11.1"
    "@unrs/resolver-binding-linux-x64-musl" "1.11.1"
    "@unrs/resolver-binding-wasm32-wasi" "1.11.1"
    "@unrs/resolver-binding-win32-arm64-msvc" "1.11.1"
    "@unrs/resolver-binding-win32-ia32-msvc" "1.11.1"
    "@unrs/resolver-binding-win32-x64-msvc" "1.11.1"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utila@~0.4:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/utila/-/utila-0.4.0.tgz"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/uuid/-/uuid-8.3.2.tgz"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz"
  integrity sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/vary/-/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

void-elements@3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/void-elements/-/void-elements-3.1.0.tgz"
  integrity sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=

w3c-xmlserializer@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/w3c-xmlserializer/-/w3c-xmlserializer-5.0.0.tgz"
  integrity sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=
  dependencies:
    xml-name-validator "^5.0.0"

walker@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/walker/-/walker-1.0.8.tgz"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

watchpack@^2.4.1:
  version "2.4.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/watchpack/-/watchpack-2.4.2.tgz"
  integrity sha1-L+6u1nQS58MxhOWnnKc4+9OFZNo=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wbuf/-/wbuf-1.7.3.tgz"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

webpack-cli@^6.0.1, webpack-cli@6.x.x:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack-cli/-/webpack-cli-6.0.1.tgz"
  integrity sha1-oc4l2lugdxUa/XOt+hLiCOUIkgc=
  dependencies:
    "@discoveryjs/json-ext" "^0.6.1"
    "@webpack-cli/configtest" "^3.0.1"
    "@webpack-cli/info" "^3.0.1"
    "@webpack-cli/serve" "^3.0.1"
    colorette "^2.0.14"
    commander "^12.1.0"
    cross-spawn "^7.0.3"
    envinfo "^7.14.0"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^3.1.1"
    rechoir "^0.8.0"
    webpack-merge "^6.0.1"

webpack-dev-middleware@^7.4.2:
  version "7.4.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack-dev-middleware/-/webpack-dev-middleware-7.4.2.tgz"
  integrity sha1-QOJlo9PSZ5VYXP+CB2MNOo/wWHc=
  dependencies:
    colorette "^2.0.10"
    memfs "^4.6.0"
    mime-types "^2.1.31"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@*, webpack-dev-server@^5.2.2:
  version "5.2.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack-dev-server/-/webpack-dev-server-5.2.2.tgz"
  integrity sha1-lqFD1QxY/vDHkQfmHfkRco186zk=
  dependencies:
    "@types/bonjour" "^3.5.13"
    "@types/connect-history-api-fallback" "^1.5.4"
    "@types/express" "^4.17.21"
    "@types/express-serve-static-core" "^4.17.21"
    "@types/serve-index" "^1.9.4"
    "@types/serve-static" "^1.15.5"
    "@types/sockjs" "^0.3.36"
    "@types/ws" "^8.5.10"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.2.1"
    chokidar "^3.6.0"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    express "^4.21.2"
    graceful-fs "^4.2.6"
    http-proxy-middleware "^2.0.9"
    ipaddr.js "^2.1.0"
    launch-editor "^2.6.1"
    open "^10.0.3"
    p-retry "^6.2.0"
    schema-utils "^4.2.0"
    selfsigned "^2.4.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^7.4.2"
    ws "^8.18.0"

webpack-merge@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack-merge/-/webpack-merge-6.0.1.tgz"
  integrity sha1-UMd2ho4IBXRyWrxYab1uTvChbGo=
  dependencies:
    clone-deep "^4.0.1"
    flat "^5.0.2"
    wildcard "^2.0.1"

webpack-shell-plugin-next@^2.3.2:
  version "2.3.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack-shell-plugin-next/-/webpack-shell-plugin-next-2.3.2.tgz"
  integrity sha1-5/+BfwB8vci54pgA02yS481kuv0=

webpack-sources@^3.3.3:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack-sources/-/webpack-sources-3.3.3.tgz"
  integrity sha1-1L9/mQlnXXoHD/FNDvKk88mCxyM=

webpack@^5, webpack@^5.0.0, webpack@^5.1.0, webpack@^5.11.0, webpack@^5.18.0, webpack@^5.20.0, webpack@^5.27.0, webpack@^5.82.0, webpack@^5.99.9, "webpack@>=4.0.0 <6.0.0", webpack@>=5.61.0:
  version "5.101.3"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/webpack/-/webpack-5.101.3.tgz"
  integrity sha1-NjOyN1uynqSwb/sZAnNNl3vEQ0Y=
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.8"
    "@types/json-schema" "^7.0.15"
    "@webassemblyjs/ast" "^1.14.1"
    "@webassemblyjs/wasm-edit" "^1.14.1"
    "@webassemblyjs/wasm-parser" "^1.14.1"
    acorn "^8.15.0"
    acorn-import-phases "^1.0.3"
    browserslist "^4.24.0"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.17.3"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^4.3.2"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.11"
    watchpack "^2.4.1"
    webpack-sources "^3.3.3"

websocket-driver@^0.7.4, websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/websocket-driver/-/websocket-driver-0.7.4.tgz"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-encoding@^3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/whatwg-encoding/-/whatwg-encoding-3.1.1.tgz"
  integrity sha1-0PTvdpkF1CbhaI8+NDgambYLduU=
  dependencies:
    iconv-lite "0.6.3"

whatwg-mimetype@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz"
  integrity sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=

whatwg-url@^14.0.0, whatwg-url@^14.1.1:
  version "14.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/whatwg-url/-/whatwg-url-14.2.0.tgz"
  integrity sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=
  dependencies:
    tr46 "^5.1.0"
    webidl-conversions "^7.0.0"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz"
  integrity sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/which-builtin-type/-/which-builtin-type-1.2.1.tgz"
  integrity sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/which-collection/-/which-collection-1.0.2.tgz"
  integrity sha1-Yn73YkOSChB+fOjpYZHevksWwqA=
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.19:
  version "1.1.19"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/which/-/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wildcard/-/wildcard-2.0.1.tgz"
  integrity sha1-WrENAkhxmJVINrY0n3T/+WHhD2c=

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wordwrap@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wordwrap/-/wordwrap-1.0.0.tgz"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/write-file-atomic/-/write-file-atomic-5.0.1.tgz"
  integrity sha1-aN9HF8Vcb6QoGnhgtMK6Cm0rEec=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

ws@^8.18.0:
  version "8.18.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/ws/-/ws-8.18.0.tgz"
  integrity sha1-DXUFpur+Kw5xLSMrQiefU7wom7w=

xml-name-validator@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/xml-name-validator/-/xml-name-validator-5.0.0.tgz"
  integrity sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=

xml@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/xml/-/xml-1.0.1.tgz"
  integrity sha1-eLpyAgApxbyHuKgaPPzXS0ovweU=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/xmlchars/-/xmlchars-2.2.0.tgz"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/y18n/-/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/yallist/-/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^17.7.2:
  version "17.7.2"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/yargs/-/yargs-17.7.2.tgz"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yn@3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/yn/-/yn-3.1.1.tgz"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yup@*, yup@^1.6.1:
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/npm/registry/yup/-/yup-1.6.1.tgz"
  integrity sha1-je/P+dqvn+rBeAKcDhO2FlY62ks=
  dependencies:
    property-expr "^2.0.5"
    tiny-case "^1.0.3"
    toposort "^2.0.2"
    type-fest "^2.19.0"
