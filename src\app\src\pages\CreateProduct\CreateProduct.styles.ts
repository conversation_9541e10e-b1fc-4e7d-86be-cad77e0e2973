import { <PERSON>lexButton, <PERSON><PERSON>Layout, PhlexLeftPanel, PhlexLeftWrapper } from 'phlex-core-ui';
import styled from 'styled-components';

const { SectionWrapper } = PhlexLayout;

export default {
  StyledLeftWrapper: styled(PhlexLeftWrapper)`
    background-color: ${({ theme }) => theme.colors.backgroundSec};
    border-top-right-radius: ${({ theme }) => theme.radius};
  `,

  StyledLeftPanel: styled(PhlexLeftPanel)`
    background-color: ${({ theme }) => theme.colors.backgroundPri};
    border-top-right-radius: ${({ theme }) => theme.radius};
  `,

  ProductWrapper: styled.div`
    background-color: ${({ theme }) => theme.colors.backgroundSec};
    flex: 1;
    height: calc(100vh - 8rem);
    overflow-y: auto;
  `,

  ProductForm: styled.form`
    background-color: ${({ theme }) => theme.colors.backgroundPri};
    border-radius: ${({ theme }) => theme.radius};
    box-shadow: ${({ theme }) => theme.shadow};
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-left: 1.25rem;
    margin-right: 1.25rem;
    margin-bottom: 1.25rem;
    max-width: 45rem;
    padding: 1.25rem;
  `,

  StyledSectionWrapper: styled(SectionWrapper)`
    margin: 0;
  `,

  FieldGroup: styled.div`
    background-color: ${({ theme }) => theme.colors.backgroundTer};
    border-radius: ${({ theme }) => theme.radius};
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    padding: 1.25rem;
  `,
};
