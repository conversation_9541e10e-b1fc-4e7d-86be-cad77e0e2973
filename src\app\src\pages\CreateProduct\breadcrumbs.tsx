import React, { ReactNode } from 'react';

import { t } from 'i18next';
import { routes } from 'pages/routes';
import { PhlexBreadcrumb } from 'phlex-core-ui';

export const Breadcrumbs = (props: { basepath: string; productName?: string }): ReactNode => {
  const { basepath, productName } = props;

  const breadcrumbs = [
    {
      key: 'home',
      text: t('Nav.Products'),
      active: 'false' as const,
      path: `${basepath}${routes.products}`,
    },
  ];

  if (productName) {
    breadcrumbs.push({
      key: 'productName',
      text: productName,
      active: 'false' as const,
      path: `${basepath}${routes.products}`,
    });
  } else {
    breadcrumbs.push({
      key: 'newproduct',
      text: t('CreateProduct.NewProduct'),
      active: 'false' as const,
      path: `${basepath}${routes.createProduct}`,
    });
  }

  return <PhlexBreadcrumb options={breadcrumbs} showHeading={t('Nav.CreateProduct')} />;
};
