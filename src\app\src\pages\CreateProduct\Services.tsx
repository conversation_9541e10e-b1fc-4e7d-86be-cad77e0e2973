import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { PhlexDropdown, PhlexIcon, PhlexInput, PhlexLayout, PhlexMultiSelect, PhlexTextArea } from 'phlex-core-ui';
import { IOption } from 'phlex-core-ui/build/src/controls/PhlexMultiSelect/PhlexMultiSelect';
import { Controller, FormProvider, useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ServiceOptionsEnum } from 'shared/enums/ServiceOptionsEnum';
import * as yup from 'yup';

import { FieldType } from './FormFields';
import components from './Services.styles';
import { buildYupSchema } from './yupSchema';

export type ServiceItem = {
  serviceName: string;
  serviceType: string;
  apiName: string;
  serviceOptions?: IOption[];
  description?: string;
};

export type ServicesFormData = {
  services: ServiceItem[];
};

export type ServicesProps = {
  defaultValues?: Record<string, unknown>;
  onFormChange?: (values: Record<string, unknown>, fieldName?: string) => void;
};

const Services = forwardRef<unknown, ServicesProps>(({ defaultValues, onFormChange }, ref) => {
  const { t } = useTranslation();
  const { SectionHeading, SectionSubheading } = PhlexLayout;
  const { ServiceWrapper, ServiceHeader, ServiceTitle, ServiceTitleInput, ActionButton, Container } = components;

  const [newServiceAdded, setNewServiceAdded] = useState(false);
  const [editingServiceName, setEditingServiceName] = useState<number | null>(null);
  const servicesRef = useRef<null | HTMLDivElement>(null);

  const serviceTypeOptions = ['Web', 'API'];

  const serviceOptionsData = Object.values(ServiceOptionsEnum);

  const serviceFields: FieldType[] = [
    {
      name: 'serviceName',
      displayName: t('CreateProduct.Services.ServiceName'),
      inputType: 'TEXT',
      validate: {
        required: true,
        minLength: 1,
        maxLength: 100,
      },
    },
    {
      name: 'serviceType',
      displayName: t('CreateProduct.Services.ServiceType'),
      inputType: 'DROPDOWN',
      options: serviceTypeOptions,
      validate: { required: true },
    },
    {
      name: 'apiName',
      displayName: t('CreateProduct.Services.ApiName'),
      inputType: 'TEXT',
      validate: {
        required: true,
        minLength: 1,
        maxLength: 100,
      },
    },
    {
      name: 'serviceOptions',
      displayName: t('CreateProduct.Services.ServiceOptions'),
      inputType: 'MULTISELECT',
      options: serviceOptionsData,
      validate: { required: false },
    },
    {
      name: 'description',
      displayName: t('CreateProduct.Services.Description'),
      inputType: 'TEXT',
      validate: {
        required: false,
        maxLength: 500,
      },
    },
  ];

  const schema = React.useMemo(() => {
    const serviceSchema = buildYupSchema(serviceFields);
    return yup.object().shape({
      services: yup
        .array()
        .of(serviceSchema)
        .min(1, t('DataScreen.Forms.RequiredField', { field: t('CreateProduct.Services.GetStarted') }))
        .required(),
    });
  }, [t]);

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      services:
        (defaultValues as ServicesFormData)?.services?.length > 0
          ? (defaultValues as ServicesFormData).services
          : [
              {
                serviceName: '',
                serviceType: undefined,
                apiName: '',
                serviceOptions: [],
                description: '',
              },
            ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: 'services',
  });

  useImperativeHandle(ref, () => ({
    trigger: methods.trigger,
    getValues: methods.getValues,
    getFieldState: methods.getFieldState,
    AddService: addService,
  }));

  const {
    handleSubmit,
    control,
    formState: { errors },
    trigger,
    watch,
  } = methods;

  const watchedServices = watch('services');

  const onSubmit = (data: Record<string, unknown>): void => {
    console.log('Services form submitted:', data);
  };

  // Watch all form values and notify parent on change
  React.useEffect(() => {
    if (onFormChange) {
      const subscription = methods.watch((values: Record<string, unknown>) => {
        onFormChange(values);
      });
      return () => subscription.unsubscribe();
    }
  }, [methods, onFormChange]);

  useEffect(() => {
    if (newServiceAdded) {
      servicesRef.current?.scrollIntoView({ behavior: 'smooth' });
      setNewServiceAdded(false);
    }
  }, [newServiceAdded]);

  const addService = (): void => {
    append({
      serviceName: '',
      serviceType: undefined,
      apiName: '',
      serviceOptions: [],
      description: '',
    });
    setNewServiceAdded(true);
  };

  const removeService = (index: number): void => {
    if (fields.length > 1) {
      remove(index);
      methods.reset({
        services: methods.getValues('services'),
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <ServiceWrapper>
        <SectionHeading>{t('CreateProduct.Services.SectionTitle')}</SectionHeading>
        <SectionSubheading>{t('CreateProduct.Services.SectionSubTitle')}</SectionSubheading>
      </ServiceWrapper>

      {fields.map((field, index) => (
        <Container key={field.id} ref={servicesRef}>
          <ServiceWrapper onSubmit={handleSubmit(onSubmit)}>
            <ServiceHeader>
              <Controller
                name={`services.${index}.serviceName`}
                control={control}
                render={({ field: serviceNameField }) => (
                  <>
                    {editingServiceName === index ? (
                      <ServiceTitleInput
                        type="text"
                        value={(serviceNameField.value as string) || ''}
                        onChange={(e) => {
                          serviceNameField.onChange(e.target.value);
                        }}
                        onBlur={() => setEditingServiceName(null)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            setEditingServiceName(null);
                          }
                        }}
                        autoFocus
                      />
                    ) : (
                      <ServiceTitle onClick={() => setEditingServiceName(index)}>
                        {(serviceNameField.value as string) || 'Service Name'}
                      </ServiceTitle>
                    )}
                  </>
                )}
              />
              {fields.length > 1 && (
                <ActionButton $variant="remove" onClick={() => removeService(index)}>
                  {t('CreateProduct.Services.RemoveService')}
                  <PhlexIcon name="remove" size="small" color="negative" className="icon-container" />
                </ActionButton>
              )}
            </ServiceHeader>

            <Controller
              name={`services.${index}.serviceName`}
              control={control}
              render={({ field }) => (
                <PhlexInput
                  name={field.name}
                  required={serviceFields[0].validate?.required}
                  label={serviceFields[0].displayName}
                  width="fullwidth"
                  value={typeof field.value === 'string' ? field.value : ''}
                  onChange={(e) => {
                    field.onChange(e);
                  }}
                  onBlur={() => {
                    field.onBlur();
                    trigger(`services.${index}.serviceName`);
                  }}
                  validationMessage={errors.services?.[index]?.serviceName?.message}
                />
              )}
            />

            <Controller
              name={`services.${index}.serviceType`}
              control={control}
              render={({ field }) => (
                <PhlexDropdown
                  key={field.name}
                  name={field.name}
                  required
                  label={serviceFields[1].displayName}
                  data={serviceFields[1].options}
                  width="fullwidth"
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e);
                    if (e.value !== 'API') {
                      methods.setValue(`services.${index}.apiName`, '');
                      methods.setValue(`services.${index}.serviceOptions`, []);
                    }
                    if (errors.services?.[index]?.serviceType) {
                      trigger(`services.${index}.serviceType`);
                    }
                  }}
                  onBlur={() => {
                    field.onBlur();
                    trigger(`services.${index}.serviceType`);
                  }}
                  validationMessage={errors.services?.[index]?.serviceType?.message as string}
                />
              )}
            />

            {watchedServices?.[index]?.serviceType === 'API' && (
              <>
                <Controller
                  name={`services.${index}.apiName`}
                  control={control}
                  render={({ field }) => (
                    <PhlexInput
                      name={field.name}
                      required={serviceFields[2].validate?.required}
                      label={serviceFields[2].displayName}
                      width="fullwidth"
                      value={typeof field.value === 'string' ? field.value : ''}
                      onChange={(e) => {
                        field.onChange(e);
                      }}
                      onBlur={() => {
                        field.onBlur();
                        trigger(`services.${index}.apiName`);
                      }}
                      validationMessage={errors.services?.[index]?.apiName?.message}
                    />
                  )}
                />

                <Controller
                  name={`services.${index}.serviceOptions`}
                  control={control}
                  render={({ field }) => (
                    <PhlexMultiSelect
                      key={field.name}
                      name={field.name}
                      required={serviceFields[3].validate?.required}
                      label={serviceFields[3].displayName}
                      data={serviceFields[3].options as string[]}
                      width="fullwidth"
                      value={field.value as IOption[]}
                      onChange={(selectedValues) => {
                        field.onChange(selectedValues);
                        if (errors.services?.[index]?.serviceOptions) {
                          trigger(`services.${index}.serviceOptions`);
                        }
                      }}
                      onBlur={() => {
                        field.onBlur();
                        trigger(`services.${index}.serviceOptions`);
                      }}
                      fetchData={function (..._args: unknown[]): Promise<IOption[]> {
                        return Promise.resolve(serviceFields[3].options?.map((option) => ({ value: option, label: option })) as IOption[]);
                      }}
                      validationMessage={errors.services?.[index]?.serviceOptions?.message as string}
                    />
                  )}
                />
              </>
            )}

            <Controller
              name={`services.${index}.description`}
              control={control}
              render={({ field }) => (
                <PhlexTextArea
                  name={field.name}
                  label={serviceFields[4].displayName}
                  width="fullwidth"
                  value={typeof field.value === 'string' ? field.value : ''}
                  onChange={(e) => {
                    field.onChange(e);
                  }}
                  onBlur={() => {
                    field.onBlur();
                    trigger(`services.${index}.description`);
                  }}
                  validationMessage={errors.services?.[index]?.description?.message}
                />
              )}
            />
          </ServiceWrapper>
        </Container>
      ))}

      <ServiceWrapper>
        <ActionButton $variant="add" onClick={addService}>
          <PhlexIcon name="add" size="small" color="positive" className="icon-container" />
          {t('CreateProduct.Services.AddService')}
        </ActionButton>
      </ServiceWrapper>
    </FormProvider>
  );
});

Services.displayName = 'Services';

export { Services };
