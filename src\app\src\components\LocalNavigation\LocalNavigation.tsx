import React from 'react';

import { t } from 'i18next';
import { routes } from 'pages/routes';
import { NavLinkProps } from 'react-router-dom';

import { NavigationBar, StyledNavigationItem } from './LocalNavigation.styles';

const NavigationItem = (props: NavLinkProps): JSX.Element => {
  return <StyledNavigationItem {...props}>{props.children}</StyledNavigationItem>;
};

export const LocalNavigation = (): JSX.Element => {
  return (
    <NavigationBar>
      <NavigationItem to={routes.products}>{t('Nav.Products')}</NavigationItem>
    </NavigationBar>
  );
};
